{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["l0bbVOJiMpqANR3Me0CtQZooFytC6qiEHn9AZfkeziI=", "yfVtEdmK4b9IMGTRmm3M2R+pPFapSt2NL1y3zRRQCdM=", "OuPQzRhQa/9s//cScwOh1yuJ5ZsTPcXe43/N11AFJuk=", "0X7b4LrZK9RivcjcBxz7oX7JsQSEz209CQLhpJfn8s4=", "ovvU+F3dQMOqhx2lNRqV8f0wO8Nfrm/93S6dcGxmL10=", "BmJLRgb7bNdikHZLnl6AwLwzjYaYy4Wvh4kMtnyqy6g=", "Ib+vaqY/DqKjBIR2BziOAjfcfi7CTYUA+VwHBAlfODA=", "s07T59Bmef/AEJav8e2+ujSCC9AsJyj62DhggLsB7aY=", "JD244JV/iS6+woUri6iVc6PwkAJYXnVXgZcpAls+U/o=", "gOzGDy0qvcrFtaoBIunqzmVw9kD+JrXdKiCTJ1usmMs=", "5sLch6Zt+/5LyaXYsuXNg7vG2dYjZ+9zvZbAzM6vjLg=", "OgUGyspCd13p4ShHWb78SgkBsh3TtsFCwY1oS13p3lg=", "T0/ut+rg4kWNzWezT8Z1pmz/LdVMteexwhWf1j0gVi4=", "NMpRFQ3ninP+rOzXVE7T0RO4PmOC1Ecmz0FNqg0QOlk=", "JDczDbuKA9nmKoHgI1xDrsz1Ome8MJYIici7xpL3EZU=", "IWWoKW6cANMssBXt4uFAGMBHoylFMtVcYVYoVlaBVyE=", "2jKtCIVuOpjiyiUduIF4CmXMyUZ6rmcdpbCfNeA+Ie4=", "6yiRTco9+/ZgmBqPVHvTqTLgimnCvYfoce9g4vmPR8s=", "99vmGA3KOPEdDL36GRKgraVB3JwhH7Eh8cueOMn6ljU=", "PR5JAtHuOdJT3MO4yAHuDwEPccMGnYfWEXJGlv+0MXs=", "sTWsLJqrEYvEMjTurk+caUhqSQYhnR0g5P/4EHRnOyk=", "nK/kIFE5lzSc8LM1C7mgeGLHfqJtQ+9WMrfQo0CGw9w=", "ZQpPV9Emzitm/J7IXuNga/cP3vdbBvvhS40qNdyiYZ8=", "W7Un/KbDS3ubDkzVgF2BMFRi06z241/uJaxA93eRZ4w=", "EHIM+YgSnMQjfa7un+065cic/cxd2KOjcNBhQoF/YKA=", "hkBdDomZSvKvjNDndpU5AHgqR1l5YdXc0DeNflfUAWg=", "ppvNPeKrh+wbEHxZgTY8mABkSVvQwG9sSv624B4joAs=", "YkXC3/VBk8+8NldflP2P+rCoY6/t7Rtv9tG9JFE76FQ=", "Pck3gLRe7ZoA0UpEBBTzf4QaFWrrLNs57FYAtLAlLuc=", "7/7du6kwDJO3aZp4HOzHwRxUgyZNte0/ipHnnJ8R0hA=", "S/u7AKirSWG+fAKmR6dQXEil+IEPtIQ3wwnjqt4HEo0=", "UNy0dsdZobsA8jMfheNBIW/Xb24VRTdwy7l1WQjXk54=", "UwXVsSvd8KKKRbsUSIQNKwxHyi6KFoyVMGFfVXZiPwU=", "v6TDPPFIDZwX60Mou2ap7W5+KF/JTzjGLbAX7TNN3FE=", "JwMeGgqYUKMh/1A8+ShDDTqjXxiEl3Zp/kkedGXadT4=", "Q9PzJQOG76e7adepktEY+yCLKmm34iOykc+WAEp4D5o=", "4Qek5TF7hQKoXGfqJDtLq9cZN7xqk274lsPdByCeGTw=", "VygPeAMvR+XX6w0HzTvDF08yJVbW8EXC9H1WS09r0pk=", "bsnmyCUwxDM8DcJUuakN90YOlswjE8U9AKOMCYqOQ58=", "hWSvR20JbEI/d27y7UZ3hM+qLb5L2Pdh+Zxaa0oOOcU=", "oTwP+5qCmL3yJ0lUJ/gctzzYPCTLZVjXajw3S8ihucQ=", "Bh/Nv1387c6XmJZd5XsBBH7RPN9kBGCjV0wS2CyBtX0=", "Tq8lDts2sbitvJG14dbPEGRkfLxkumg06yL4GHmcVL8=", "wqLKdlr59eoYhdlCTcklFvFM71V/fdLCh8BQKfNr1+s=", "t2KijVwC3yEHq1F1C7iUrQDjOz3U/6OUluZcUf2Hi7w=", "RUllg5HOBQupLbOU5W56+twTmc9JTgzx2TnXh/gq3hk=", "S9ZUdCpb9zaC2UTxpYKa+wq4nVx+eTPoawxhFjTzA4I=", "PQKPusuubJeq8W0kZAxncJfQibT+4l95BU9RwSFPE9s=", "z/YM8cOkPfRlQu8dFNmthUBCyj/G/GwKVGpgolfudPY=", "7cAPD48+BtEjtOzzuDszcicKubfNWNsPwrgQBXcBJdk=", "XemN/G6TvhizZWCTHiX+4lIgXaC+gGRB9eSd75uhCyY=", "OHPKBFJ2z9s9g1fE1dFp8sPMUcPqpTiYhJpE0tmJHm8=", "fqBfAuEdn0LEhcyTxr5dXgqV0X5uADN2dReYSRq1IyI=", "rM7FWU6vve9yTbx8VLit2JnZVZgw+Sx8EOpzWRjj+50=", "FIlMC/PYtKGPxj84SjYEkqYHFtbRTvdVmlIJT/JPo3w=", "/LQ8WXT2ntE3SytJU+8IXIyUbprsvyBUKDimmFaNBKM=", "QVycbN7ewaKythqLA00mo5zn2ndJiQYqKOrTgk9IMbA=", "b/EWpFOTHrUJdx5t9eCvJYPcXkAA3AwbyCpEQpzI/Dg=", "WJ55jS2Ei/Z7KknvUtHCL03twK7t03taqOALZsNuAYU=", "oMZfH2qTM0SFF/2gvGkkNUhKgAMxPTm1hr2a2SYTSpI=", "AyKK7IZXkoBqSlxAgyfeaXem2UhM4LgX2JhvSlgj5wE=", "zT2yUWVn73p/GZ/bc/spt+fcsnF9kEvfCAUWm2V23AA=", "lbWVWs5kKr6C5IRQ1iR2pPEoIibLsKqk3mbB49QVQHs=", "BigFGSAf4AFLciwSkawjspF+Viapzx2K51pqusDtM1M=", "4xcY0eQjcPC2tllXSukoYEhzvTl1soGwYRy0w5SX4Yg=", "51/zIo/w/3WuuDbEWIJYHq6m1PF9qPRy+o3d+nuMwqg=", "mn4iFpFBOSKony3w04tmFEMTlaUX3GNpGDQ9NvTd/IA=", "Z1S0PJua30eHBDP5J8y8C+5XrvGtNESoKhDQMqDRoos=", "L3lak43P9Q8SlF+ciOaTcNRNg3RjIOP4gfqqsW0clzg=", "YU/1P6SZzkjNHhZfxPpSICJxzXUXlAAR0XutgIRTFy0=", "sIJ7+4X5/gVON27C3DLTZXP5ViLPVmnd/52sCeIsLGA=", "imfUG1ZO7dZRPd0Uyuxxqu/kxKDU4GpJMQTae30F6B8=", "Hxlej2AihGaDXT20S+9rjILdKK2IfPIBbXt6lAtXjtA=", "MHatiIJ6uiREx33nxYzDaK8aQNZEGyeWMWrqQ4SK1mI=", "jrtHI9d6cYw8tmjRINyhkZqTKjlmNPn7lFIWxXSg4J8=", "iswHQcsifAVLBcbYJO0vgVRd3xgSdIDf80vNq+5THRM=", "+Nb2pGPeCrVXX/I5StYWdadWN8RrrSeJDEVCO1YVEFY=", "Zw3UdLm75ekA/bbL/CnsbUZDhOLaRyRkMOp+h8OM5bs=", "ZxSwp28GddE6mc4Ybh1iW7TKBqzVu2OYw89cc46jUvA=", "65DHbea2uYlaHLgxMmyKpQfIOaRIAhtH13V8N2aB0k0=", "9pdEoPtwTZ8dNrB1Xq4z2jfuA5Wx8LAvSI57zY47x7U=", "nDj/UXFbLUcXmFByWdG7dnQVU3rGj8Lyky0X7C/HA38=", "NZbc0JQWucIz8ius2LKTbiEQ4XmN31ysuexcg1Sb7Oo=", "qSrnNIL86Y616qhga+kFOAusVqck2fpuGsrxFD3M5F8=", "QB5NY153jxTOCRzqUds0XCrkXCcHQNvpdkfedvaIKh8=", "bbcRzOhk8whtbFQl/jj8Cs3MbFWFDLIpSNCpi9oT7eE=", "HB6qOhUgeDeNL71y8QqVUNVESkObt/6eVrpMcRTDC74=", "8U5fxXxDCbXZZ1v1Z7iq2WvEIPbqMqX96vVWGmbE3yQ=", "e++gomCevWNMPmLbh+rM/nktFC2GD4dTIXO0NKrY4Yg=", "pKQhfDP6U3Mjm4MnDPmU3s3kyg9e37mR8JBBpxhIMjI=", "TyyyjZU4e7dw6w5mu2Po4PMPCKKBiKtu7FMTWGcYfaY=", "hMZE2A+IYnh3A66hlXNWlkGRVf5iK8ei4LA2lem8hcA=", "LX6TPndOG8sYTM89cETRXWRdXleT3qe9vv0wWC54VRo=", "13s0+HaD9AaPfWvma0wFjBGBYm60az+827iu3pTxqlk=", "etZgoQ3vMO1Glq7uPhgqaBMAvpuhUwNhOcMdFwAjZ48=", "eZuohYJFMi3Ev0TAI3yVebJyJ+yfqW4E/sQRZla/FDY=", "9fNzT4J3DMvgouI7LfuVKnCCxxmYO1D+fbXC7qGbIiw=", "3JLrpOEOU+vKH7rktf+S1Sn6QDCmP/L9mI/T8+KMkCg=", "l/LGUQ+nYQS1hgkwEhdzVpAE5iZB+XBBcI9Eb4fxvyA=", "tYAFaPZwGTGqBMSXQH3EwRtkL7OtUKY7bWFxeJxHdNE=", "B3mGI6me12Sfuqsxmj4A8v2GHH47PdDjKrzuvFCXEWE=", "4+GZcBEy31DI1hCICgE6OQiOz8gL1TzkG6j7VxQCu8I=", "r7LurTRzXb9hL9xTbzUK6DfyUfNRseHbiUb0U9UwfOY=", "aeakI2BiNS9x5Ee/Ff2urIJU3fEAoFws4gtR3GANiUY=", "s3kKUuTdlepXI+S+OxLRA6PXGOQIwwTo3P+3WzqG3Ew=", "eG3m4DnV1ofhpZJhLtBjVhifW3/CSHxO5DQPymJxYGw=", "DGV77qP+uPh8KcrAeAqwS3KivDflFAGRASmr8VT+sgA=", "i8kivjONG54uzMo0XkkndA7K8zn4D+ydLHm5aPJi9B4=", "FfFcuETizuGEPrRARWaDYFKMY69vnRkp7QfXAPeOgzs=", "fgXT0haUuM4eCeQ0A2O91LauMCppT9gPEn5R1W8TR3E=", "dMleq5fNwnQCcq4caEQvtwnyMicDN1Mk6h8fgHjB8q4=", "eunOQl66LxrSTjafA+cV8uqTDk1wlEGc25C+5sOxUWw=", "qy2wt1PuH/yQsHzHsxYABF7zUB19k7l2H1EIUu6LM7U=", "F/SEhoGNYItWgS9NnAJqApszDXmezHCyphSEw/wWiLg=", "eG4zrvPf59ndQa2uTxsEyPBZMLhySvMoDig34XbHMn8=", "ZZoeHhOomyF8o21fMkihSvsDJprVrGx63DR0H9Nwmrw=", "32j0LZsWd6ub5oHi3o8TY0z7XEbl8AzQHdRn+n69Kg4=", "6TboRCiL8vp8Ttzo3VeWhg99CRIrAr4VP7s1VC+9Eds=", "CUkO/vYzqSOHpSk4JUEnsaIzwnQCL46H9lcNVko5gwA=", "5uNl6qiDFH4ZOXwaT7NOE+oCmq+1GSCp659pZ9M/2I4=", "2uDYD/jo3YV5DKm+jAy7Hydr1ZamlXH96+SbS/F0T54=", "wG6/xpMvjF8dLF94Wr8iVXXX7dVt5kzGMlgNXy3JhWg=", "7O9e3cBfLjPnhFU+6iDIIHkqeUSC9y3ELFq4dBGcaK8=", "ny3CcJz40gw84tiYgM8ucK30in5RdLFD9TaJiCW+hOw=", "joHfRPgRZdt5U24b/s3+bJyge9ad36W0ylCk0oH3AYM=", "YCmZ0InD/LDIYmCj0UDb/0W55golZMrP1XASNsQxENo=", "mWkG/UeGUPk0IpeXgxnXdkplRT53NzZgY2pX88ITpRg=", "Gdv0VAMdgoaCwn1Pr/KqpLEWhMUYAbmno8GM/ZARF+E=", "BTkJ4j8Z8m+J52hBovbFn8G5ciptQTMyzkgwWNiiQfE=", "Dl8a6SutF9jtyt5wzqmLLEQSz70zGX+5FRV5qO22wYw=", "Jkuhg+iJ0Be6T6atcVzERp4Gr9V82xYoX5MrQB26eTA=", "c28affXMVQ5apPPO0PBVnePMdAUwp6MT3zVcjJ1LqP0=", "4sghIsldvLdbRwfKCeBliuM21r1r1P3MVXGP1RlYA+I=", "eeIh8U5hEXHQ7IOl1DfuUAoDxDXmSmWlAQSOd978uhw=", "m6f/jZ3018lPV+DE0rUidbhkUNB8/O0wew5azgFDB0M=", "uWJFcxN1lacqFSR7bQfAI3DBIXz5xFx6JJsieFvsgX8=", "8/pJdwvrCC5krr4/luOWqnGQBpu7HulcKxaTm5KjcwI=", "v1zDHcppGbp6QqZOyZ9k5kPvZohs03DgnEbIEboAbds=", "Q3s00DvZ2nSCzACiZaII/cN+kuNBl39Ufg892ygfGgg=", "muGElj+GmqOysJfeILfDoxQ3dA8ftXIQuchjrZNZwaY=", "NNYRijhDbl0duI8NwG5f/jAvZgL0ksE5+AuU2bm4IeQ=", "J04TZ+JJWqpIy3sM32OVlvsrM5vQBybxu4JBXO/NadA=", "wS7kT8OyjYiWxyCGz/mXMqX6hRtn6hSl6fuGtcu2Zmg=", "lvdQjTL4ntUUayG28cAcmZbhMYmpIQ02bE5mPFeV/f4=", "omYy7fqTQ3Vt/dMbsvKhDutW5qc7VSLKdepkkvRzieM=", "rz+l1zLdmd3G7XAQq7lizp2TWEAURPlGaWBa0KfV4wA=", "vqVytc9EIvPnn+am8hne2+TgJ8i3DPf/BIbWIWgNbMo=", "YDm4Jq1IvcDVjH5QBgMEyM7owZHl7G+aktkH0G+yfTU=", "o5Sz8hozmyyPeIAxgGMcoAaUYHKjB1H30NDm5e1Z2XA=", "xPoSjufjpUGqGqXK4fC9bWGfd0anLBQvfmBBv0OTW+w=", "nApqBkNnmun8hGEZ9XZWN+lRrm5ECPSEljoe++hAS5c=", "rCmgJ58cK2rkg2tzaEO+C7laDKkANLZUBCT/k15KDHM=", "zdjl36uH8OmwSP/wtjWaWQQpz3gi9Wf+c6GfaVPoTQA=", "FC1e7eCxmJiQ0VsVDcocV0tqMAlaX8+PLdbYKDDcsRM=", "pZZwYaCDPGkYp1eAOM4wMWa5BadBruxhKWbYm3WS7PI=", "f6GAj1Ac9t02gGGK8c3TmXSUqEq0geN/Dt9pdlrMOF0=", "WtYkV+lW4XrlB6MlIAD/JEp1qtenjn/C+B0n7aUfp5I=", "25TXBdiFFmglqCCTQN6iiY0Ljvyygl+rFeGYixXILIU=", "EJrDmIZmdBNAlMy+IRqBkaQkH9heG6pS0a0b5CQneBc=", "zhYnJD+ajXwkGjYx43nLMtZsROWV16pm5LyfBlu1HFU=", "bhsueBkvl1aRWOAMNAUt/AbWSdHVSGR2sQK2g3MjLgQ=", "Mr7ft6C+W0+Vs+hxifdCCXgm25M2o6t33jbSQq25ymg=", "Xc89MyIRLSL2Gw1TDQkJZeLTw7SchPvzjjxPDX/p6Rs=", "NXfe9ySkgtT+vyhvi49rHkNec5xd5ixkcNfQsClkArQ=", "MeZXjG26Bogpa8apHgvkb0o6ewFjTsYByc2jHK8ECuM=", "CKMuGBNhZJYhK7RLxxd3J5da3sPCpPmQZN4si3ptqAc=", "Op/jJePG0VpGRU4GbiWUReZ2KqaIAbRrFkPcON2UW/Q=", "tZlrvGHHtRxZtqnBSy2uArNM6yrTycFfcaF5ug8BG/s=", "icBel0gw+SHoEHVCkEwi8ECUbRr/FaDY4vUnS/9NP/Q=", "ePv7Oz2ME7yfAZYziCyTpovACnqwCdGFFLiNfsceZb0=", "vGWa1+rROg+VSugcucr36pBxAn9QZtcgw46XCz0f/Mc=", "4tlGrxAed8BhzFTWWY/6NTRX5Mk5wMo5igJ0V4nCFBs=", "jT1VlNlqDqKGBy2SgJIPSZTFnIuXO5ge60gX9zEAK+g=", "9cHxn3rmmaS4CmvH1cIBUg5csoty2BMabDDtBHazSxc=", "jrcF4V74x4spLFXPZz4gCUMQSR2QZW6aoZN+HoKb3zE=", "3QqzkAjfEWoZGua1uqoDmjUEGivQ9JTSDaMXiT2gv28=", "9eO3jpfBMOalq+iuffSSMeKreDxs6x1AWM63rHbeL7s=", "wOl7KFVvsgnINhWPDl6mgtW0Xc3RqlkeRnv05A2xZYg=", "WxKsRKkoDb4pFNNQQnQSDGpobYV93fnbwyhjAOyeJKY=", "2FefMRCsfSk+2AigRGy7h2oABAAsn2KdIGXxLzttKjY=", "F+hNZefmWJjQrhGwpCfuKcPqZ2XT6QnYs9a+5HxQ3GA=", "JYkBU9qoGjPSXw6HNZoxYpGGUYlHTmOF0nidkGoGvM8=", "l9kqDd2c668cWoXQ0zI0mPtS0Q5CVR1Xq8snT9fBmo0=", "yeBKGFZg4+6t3/S2Zp4s3d4OBKTGh/WoNSGg3SFB1t0=", "qqWBEEq9L1FHkj4C2HxPVEioqPAJ6Gu/GfU4uRGQNG0=", "lf0AO/018kh4b75FCRI4waMOFtHZ9bLSbejjL2Xu7O8=", "L/qr6Mr6aICCXLv5j9UXslr2fcvlQ7NHeFxR8OzQrkw=", "qMzce7CTyWJ2Ri4l9kjPJirog2cIh0DWeieNRv76oyw=", "jBiWjbtVo2ydk1bpAYn/jL0EaiJlDyfFJpEfjJS6eR0=", "xdOi/Xel8Ge/OV+APXpC2S7mwTytduTFZSiDc3+pIbI=", "H9jBEoRpyTU23/G5EM52xib4Ob8304i7Bu30+3oxgTk=", "rgB/TCBvpFG+wsPOrHPo0RqUj7ocro/VL6RHvZs4L+Q=", "+0f/8U1pIjmYhj6nVL5KE3gvd3OgzKyle1puRCDLnqc=", "FAztQszHZzVjSZa1oLlGtmN5mq6S0WYqFW0Dph2pN6c=", "FgThsVcuZhZa7NRG/AiL/MmiZjPnDBZSnzb9dQNKv1w=", "wvcP8HVnUUHk+zinwHKzXfmG2jxneX0hiq3G/Zsb5f8=", "YAc8sybiaSpWFxDF250dK71RLmO7UU33IeTxkAubV10=", "WzTGqhgF0sTvwROlNFOM/IyqF69+7pIq7SeE7i0cAL4=", "K3OC9mTHEIh7pCcdilKmQJbsOCZzHQGcDr/bm/8Tmcw=", "LHdH1n4qlTz9HzAzmsZ8rbcHH6G/nhv4+8cBZFK1c6Y=", "0xhBk2T29PPQrbkmp6DhxvbGdlES8fvJ3HQWT+RXIh0=", "Q05WbS3sUcXcF0SdiPsBQ2f3Oa+MZQ7nkCWhUmE2TSY=", "8sLqBHsvfxM5EHrVJb+Jp6odtBCZ4bPM251JSdhUAuU=", "CGvxP2JiRnBtt/z0rFZ3n8IFQ2CCVEIk46QAPUSjHwg="], "CachedAssets": {"l0bbVOJiMpqANR3Me0CtQZooFytC6qiEHn9AZfkeziI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\spvv7kmwbr-s4opx6c2mv.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1or4nzsr4", "Integrity": "j90XZRBGRdvdN733vN1Cu41wwWufjeJ0ph/WbbCtz7s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 20249, "LastWriteTime": "2025-05-30T11:27:03.1967648+00:00"}, "yfVtEdmK4b9IMGTRmm3M2R+pPFapSt2NL1y3zRRQCdM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8aefelkhn6-8nu8tgp722.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yal1nap383", "Integrity": "GlGw43Kbb8NBidMY/EYHmgCkd3vekVVoTJ6UxCcOa/g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.dll", "FileLength": 24310, "LastWriteTime": "2025-05-30T11:27:03.2007643+00:00"}, "OuPQzRhQa/9s//cScwOh1yuJ5ZsTPcXe43/N11AFJuk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\mshw5btxb7-krv4lrn43s.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fpk8vhkdpc", "Integrity": "0/3UJbLFTL9DoJBOch/kzlhTtRkah3Eq+Vl6wPo9Nng=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.dll", "FileLength": 101326, "LastWriteTime": "2025-05-30T11:27:03.208761+00:00"}, "0X7b4LrZK9RivcjcBxz7oX7JsQSEz209CQLhpJfn8s4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6l4i0ftn1a-idmm1ywdgw.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6augqf9ecn", "Integrity": "svzy1v88dEHLdvhafNxBP79P8LySlKfltsNrTOMWjy8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.dll", "FileLength": 18769, "LastWriteTime": "2025-05-30T11:27:03.2247619+00:00"}, "ovvU+F3dQMOqhx2lNRqV8f0wO8Nfrm/93S6dcGxmL10=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\i1gyk9ziio-zww2naw4ke.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zpiwsz9fnz", "Integrity": "OYFWBEpyC2yyPFmxMSRMwBZK6brbzMURxlXN7fkqEhU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.dll", "FileLength": 59055, "LastWriteTime": "2025-05-30T11:27:03.2237618+00:00"}, "BmJLRgb7bNdikHZLnl6AwLwzjYaYy4Wvh4kMtnyqy6g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\gn7yidkud8-hbc4gsgkwv.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oo1jnq24ff", "Integrity": "uDFeGLxy+6qjowGJMVAHDUTI+mragBcTm0YVS+NDNWw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.dll", "FileLength": 45711, "LastWriteTime": "2025-05-30T11:27:03.2317647+00:00"}, "Ib+vaqY/DqKjBIR2BziOAjfcfi7CTYUA+VwHBAlfODA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\7r9xucsfd7-f2uz294zxf.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "11a7bsndgv", "Integrity": "e0YXewkoC/PkM7HAsXKnMsViZ68y2V/OT04MX5Bz0WA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.dll", "FileLength": 10174, "LastWriteTime": "2025-05-30T11:27:03.2307657+00:00"}, "s07T59Bmef/AEJav8e2+ujSCC9AsJyj62DhggLsB7aY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\xzuvqo938c-i0nd6gq4qm.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lu8t123roh", "Integrity": "81Sy2MBAfyiijY1FYZp6CzL3361fy9rG8cwsfmLzor0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.dll", "FileLength": 21225, "LastWriteTime": "2025-05-30T11:27:03.2347634+00:00"}, "JD244JV/iS6+woUri6iVc6PwkAJYXnVXgZcpAls+U/o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\wo786rvchh-eiih8zmtmb.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e4hp9qwbzs", "Integrity": "EsxQSMl7afXlMQ3TGQT8RxcgDsnsr4SmYFhYSjmZJs4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.dll", "FileLength": 15564, "LastWriteTime": "2025-05-30T11:27:03.2397616+00:00"}, "gOzGDy0qvcrFtaoBIunqzmVw9kD+JrXdKiCTJ1usmMs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\fbqh7pisy6-4er1q3mawy.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u3feq76xve", "Integrity": "+rnm0mePYqkfGn9OiG0H72uZXSJ43SPW/gKzUVk7G+E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.dll", "FileLength": 21603, "LastWriteTime": "2025-05-30T11:27:03.2577631+00:00"}, "5sLch6Zt+/5LyaXYsuXNg7vG2dYjZ+9zvZbAzM6vjLg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\akemijxqqt-boe436zzti.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tcyr63tc7m", "Integrity": "U5fDvqwMPPPbY5nMdLwBs2FihzzPT9LHealbqoYznOs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.dll", "FileLength": 15411, "LastWriteTime": "2025-05-30T11:27:03.2367634+00:00"}, "OgUGyspCd13p4ShHWb78SgkBsh3TtsFCwY1oS13p3lg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\o6hg2ogch3-3w9ekc3sj8.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ubwrg4w681", "Integrity": "avxrysytRoxOCVFtt/kmGDJXwLZ6rlwX+Q3GqY+Pxwk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.dll", "FileLength": 15052, "LastWriteTime": "2025-05-30T11:27:03.2417643+00:00"}, "T0/ut+rg4kWNzWezT8Z1pmz/LdVMteexwhWf1j0gVi4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\hxek4u1bpw-9ahq6pvixc.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rijfjwtaeq", "Integrity": "k9O/2yeB4vY73zDA5uQAtSsa/a5naMvrsbrfqZdunHg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.dll", "FileLength": 40205, "LastWriteTime": "2025-05-30T11:27:03.251762+00:00"}, "NMpRFQ3ninP+rOzXVE7T0RO4PmOC1Ecmz0FNqg0QOlk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\1uicwcm6p4-mu447210ox.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tfqz2p8xxi", "Integrity": "mXYeHD8Nir9RE8ORcbZiO2RlOF3Pp7O/PZcdBhB+jq8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "FileLength": 21657, "LastWriteTime": "2025-05-30T11:27:03.2647624+00:00"}, "JDczDbuKA9nmKoHgI1xDrsz1Ome8MJYIici7xpL3EZU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\u0tjxmatp3-m16opti6mw.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lqmpkje4qv", "Integrity": "oyBxrIbpImfBCOHxjUM5iT2HOahgOfEGXd0zIs8xeto=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.dll", "FileLength": 12891, "LastWriteTime": "2025-05-30T11:27:03.2747625+00:00"}, "IWWoKW6cANMssBXt4uFAGMBHoylFMtVcYVYoVlaBVyE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\o6ig8h2r41-vi1xsib3zw.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "no8wknp5a6", "Integrity": "9xO+v9EzIjE48w3Hy4IyzjQy++Oxv2UwYPo/c9u/a68=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.dll", "FileLength": 23658, "LastWriteTime": "2025-05-30T11:27:03.2937628+00:00"}, "2jKtCIVuOpjiyiUduIF4CmXMyUZ6rmcdpbCfNeA+Ie4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\n0sy9sf2fw-6d0d6d1ost.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "veuyx8euxt", "Integrity": "ifBwa0nWzJeLck+cNlqvYZLB2KsrJxFRkCRMmlGpNNg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.dll", "FileLength": 23943, "LastWriteTime": "2025-05-30T11:27:03.2967646+00:00"}, "6yiRTco9+/ZgmBqPVHvTqTLgimnCvYfoce9g4vmPR8s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\7136ysyehp-w6f789y4uu.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uiykxxj3xu", "Integrity": "Oj/iIklHFqep4wRJs0QS4zCAKGODCivr0DC7tccJV5g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.dll", "FileLength": 24243, "LastWriteTime": "2025-05-30T11:27:03.2987642+00:00"}, "99vmGA3KOPEdDL36GRKgraVB3JwhH7Eh8cueOMn6ljU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\o0pf8fvu98-a5f596hkdg.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxlzo3grlx", "Integrity": "zx0r711MHp1lXuw7V7VWmByOtb/52D2qACQ/A9LNvfA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.dll", "FileLength": 30466, "LastWriteTime": "2025-05-30T11:27:03.3087621+00:00"}, "PR5JAtHuOdJT3MO4yAHuDwEPccMGnYfWEXJGlv+0MXs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\uf6fopcoqp-vsno8mpsaj.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wh2pffqeb", "Integrity": "Eowwjej19jlp1+9vnj9x4Yy6hBX768JC4nB9FcB86j0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.dll", "FileLength": 29072, "LastWriteTime": "2025-05-30T11:27:03.3747616+00:00"}, "sTWsLJqrEYvEMjTurk+caUhqSQYhnR0g5P/4EHRnOyk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\tjbgqewee9-9zqro3rcxh.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "02r0dolv40", "Integrity": "CNq4eqpxBpyPKnTwXsa5vtral323GWqU1l58rtKv6CM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.dll", "FileLength": 21695, "LastWriteTime": "2025-05-30T11:27:03.4117612+00:00"}, "nK/kIFE5lzSc8LM1C7mgeGLHfqJtQ+9WMrfQo0CGw9w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6ze9t6kb6r-v6ko2w0hb8.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.JSInterop.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ktsbt22h7", "Integrity": "WFyloDaiwpfrz8xSkja6/TBTytE4pIZ2Lz6/sVv3WB0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.JSInterop.dll", "FileLength": 31685, "LastWriteTime": "2025-05-30T11:27:03.436762+00:00"}, "ZQpPV9Emzitm/J7IXuNga/cP3vdbBvvhS40qNdyiYZ8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\75nsjnnlxn-hdf97ld14h.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "saqhe1b5zn", "Integrity": "ZAFKtSln1fFt2udDPv+r0SeZ2ZqWGIKtTKNYMvI7AG8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.dll", "FileLength": 13630, "LastWriteTime": "2025-05-30T11:27:03.2807638+00:00"}, "W7Un/KbDS3ubDkzVgF2BMFRi06z241/uJaxA93eRZ4w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\45ldojie1j-7u4f1vnlb7.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Pipelines.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d54o2<PERSON>hr", "Integrity": "2Mt6TGvrkRHKiPOH+lG6nt4oPSinMVtZRjeQtD+EmvM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Pipelines.dll", "FileLength": 39021, "LastWriteTime": "2025-05-30T11:27:03.307763+00:00"}, "EHIM+YgSnMQjfa7un+065cic/cxd2KOjcNBhQoF/YKA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ky55uqgmml-3l6rz51seh.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.CSharp.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fuvxb3qrqc", "Integrity": "d1bEWmfyZ/hezjLGTydCdsLlrCKVTYC5X9Apg0/e3TA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.CSharp.dll", "FileLength": 140866, "LastWriteTime": "2025-05-30T11:27:03.3827622+00:00"}, "hkBdDomZSvKvjNDndpU5AHgqR1l5YdXc0DeNflfUAWg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\qkbpl08y7g-5vwfddg8g5.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2nyhpvbita", "Integrity": "no5yrsIgpG03OE++1jfFNMdaU8s2kheIk7z9Px7QaPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.dll", "FileLength": 178156, "LastWriteTime": "2025-05-30T11:27:03.4247606+00:00"}, "ppvNPeKrh+wbEHxZgTY8mABkSVvQwG9sSv624B4joAs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\yhjaqzo8sq-eehgvpbhsp.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.VisualBasic.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5zyf1ntt0k", "Integrity": "LzSUTVnCunCoSOnuV+F8US82XONAdyc9I61nCJPufsQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.VisualBasic.dll", "FileLength": 10520, "LastWriteTime": "2025-05-30T11:27:03.4527613+00:00"}, "YkXC3/VBk8+8NldflP2P+rCoY6/t7Rtv9tG9JFE76FQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\barmuuuq98-gozdvr00x2.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cysolgu8d7", "Integrity": "lP2UtP0UV9VjyjduBX/672/v9gvxE+mdIIZYAQKEt1U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.dll", "FileLength": 9665, "LastWriteTime": "2025-05-30T11:27:03.4777608+00:00"}, "Pck3gLRe7ZoA0UpEBBTzf4QaFWrrLNs57FYAtLAlLuc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\dkp6sw0rky-1eyvro6nt7.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i167qj920i", "Integrity": "qk+quee0sr2wU298OeyBTw/uX+vXjkmbY2DCFp+piKc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.dll", "FileLength": 19715, "LastWriteTime": "2025-05-30T11:27:03.4949379+00:00"}, "7/7du6kwDJO3aZp4HOzHwRxUgyZNte0/ipHnnJ8R0hA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\sbibzdbb7p-wpk5jqss43.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.AppContext.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7u26xk1wdx", "Integrity": "3/SuCR62uDtXrcW8bsJ3aDir7RtiA03lhbfDmfg3wtY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.AppContext.dll", "FileLength": 9650, "LastWriteTime": "2025-05-30T11:27:03.5169385+00:00"}, "S/u7AKirSWG+fAKmR6dQXEil+IEPtIQ3wwnjqt4HEo0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\bralpuvxhx-eh6u92zj03.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Buffers.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n50uben6km", "Integrity": "Sy+wFRlFZV5LoB1xZuk1V4b8pVowAEFJFyglvdzDfDQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Buffers.dll", "FileLength": 9658, "LastWriteTime": "2025-05-30T11:27:03.4447618+00:00"}, "UNy0dsdZobsA8jMfheNBIW/Xb24VRTdwy7l1WQjXk54=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\u0cawthvf7-uvt7ffrv46.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.Concurrent.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9wtx0yy17e", "Integrity": "Z8IAzFpC0pfqzqMSWQALZuo2/YnVqjXc8/YgJ0TCKb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.Concurrent.dll", "FileLength": 39943, "LastWriteTime": "2025-05-30T11:27:03.4804208+00:00"}, "UwXVsSvd8KKKRbsUSIQNKwxHyi6KFoyVMGFfVXZiPwU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\j9fsedl0vg-pasw3hkp1i.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.Immutable.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uvudfmplds", "Integrity": "mjS16tc9kgMTLCm763Hmmp6MKHnOuyg3uItm+Q3qEMk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.Immutable.dll", "FileLength": 83834, "LastWriteTime": "2025-05-30T11:27:03.5259382+00:00"}, "v6TDPPFIDZwX60Mou2ap7W5+KF/JTzjGLbAX7TNN3FE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6refzz1ja0-7gp753g7sk.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.NonGeneric.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gg76q2jpzk", "Integrity": "wIJmREO3VSGvCewHqZQbAnbnkeeJF/k/3mWTVp/j+2I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.NonGeneric.dll", "FileLength": 22290, "LastWriteTime": "2025-05-30T11:27:03.6199402+00:00"}, "JwMeGgqYUKMh/1A8+ShDDTqjXxiEl3Zp/kkedGXadT4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\4ipjpuljh3-j3x3ay3ee3.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.Specialized.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yeus2excuf", "Integrity": "+tKyPsxrnxZjBj5QlS5xanQhjnms7bwoWi1M0mmXybE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.Specialized.dll", "FileLength": 23915, "LastWriteTime": "2025-05-30T11:27:03.6799375+00:00"}, "Q9PzJQOG76e7adepktEY+yCLKmm34iOykc+WAEp4D5o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\tugsdwfoc7-g75y0gnocr.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g448c8pzfl", "Integrity": "4MLMQ3VIZvgAFo0jdAAX1yLwEIVrUK1TlFRxoRsH/XU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Collections.dll", "FileLength": 47037, "LastWriteTime": "2025-05-30T11:27:03.7599391+00:00"}, "4Qek5TF7hQKoXGfqJDtLq9cZN7xqk274lsPdByCeGTw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\7lmtofr39j-ycn2nv4j1z.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u9ikme5efv", "Integrity": "fsiaQxOiUljY96cM6mD283Yi8RIdqxwfwKGnY8K63jY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.dll", "FileLength": 41568, "LastWriteTime": "2025-05-30T11:27:03.834936+00:00"}, "VygPeAMvR+XX6w0HzTvDF08yJVbW8EXC9H1WS09r0pk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\cxxe308vi2-934oxfc2n2.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ya6hdus0nj", "Integrity": "1qBeqTjLTk/Yd71waxwgUxE6s2q0r5d0gD0oUu12pDo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.dll", "FileLength": 10333, "LastWriteTime": "2025-05-30T11:27:03.8839387+00:00"}, "bsnmyCUwxDM8DcJUuakN90YOlswjE8U9AKOMCYqOQ58=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6fmclb41sd-a8uhxxkrgm.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g7a332sueh", "Integrity": "lD7oJTJyMo/i3Y+JQYEpUJim3I3f6dkH3L6H4xqdGgs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.dll", "FileLength": 14685, "LastWriteTime": "2025-05-30T11:27:03.9549384+00:00"}, "hWSvR20JbEI/d27y7UZ3hM+qLb5L2Pdh+Zxaa0oOOcU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\hwypujwn1e-etirec3uem.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hxrem35p1l", "Integrity": "5dbSbdJJhRFGtrntecTXwg9dw3IwBAf6nhX0CNdQfMA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.dll", "FileLength": 21171, "LastWriteTime": "2025-05-30T11:27:04.0119392+00:00"}, "oTwP+5qCmL3yJ0lUJ/gctzzYPCTLZVjXajw3S8ihucQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ev4ztpc34h-4uq7u4gvyh.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q7kb<PERSON>z<PERSON>u", "Integrity": "6rkdmym0Ok7dulqrj5cqKgvaLAthsigfBhpSRTq6etg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.dll", "FileLength": 126862, "LastWriteTime": "2025-05-30T11:27:04.0549375+00:00"}, "Bh/Nv1387c6XmJZd5XsBBH7RPN9kBGCjV0wS2CyBtX0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\xe16jc8fci-47p2rq9wcr.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "go16j353c1", "Integrity": "AMPOvU27Ffbu5pJPKEeiaa2/y1BoUWmrW83D3DeS3Nc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ComponentModel.dll", "FileLength": 10208, "LastWriteTime": "2025-05-30T11:27:04.0669381+00:00"}, "Tq8lDts2sbitvJG14dbPEGRkfLxkumg06yL4GHmcVL8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6s308o4pu7-tj2pkmsvh4.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Configuration.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ze4dqti2v", "Integrity": "NLbn5P9dnuP2GWWm2HwblvBhD55Jcqis1EImVr1rbjY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Configuration.dll", "FileLength": 10895, "LastWriteTime": "2025-05-30T11:27:04.1029379+00:00"}, "wqLKdlr59eoYhdlCTcklFvFM71V/fdLCh8BQKfNr1+s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\1m5m86bqfh-11ylh83rio.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Console.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tgargdev5m", "Integrity": "vXGBw+sZL5EiZaX8c9e+b7bJB9AK6rmzA0akxW7Rw3I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Console.dll", "FileLength": 27934, "LastWriteTime": "2025-05-30T11:27:04.1539374+00:00"}, "t2KijVwC3yEHq1F1C7iUrQDjOz3U/6OUluZcUf2Hi7w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ednvp52rn4-xmfvpq1a4f.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Core.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k00b6zk5gv", "Integrity": "LHnZsBu2ASfdkM4PVEE3bJyUe82KHJQBR8IQrx2niDI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Core.dll", "FileLength": 12228, "LastWriteTime": "2025-05-30T11:27:04.1869389+00:00"}, "RUllg5HOBQupLbOU5W56+twTmc9JTgzx2TnXh/gq3hk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\hf5recczq3-6b5k4aahj2.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Data.Common.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nqelom5n0t", "Integrity": "33236lBXCT1uZp44xv0UIH/+DdjAqDNMg8+jBfZR060=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Data.Common.dll", "FileLength": 386058, "LastWriteTime": "2025-05-30T11:27:04.2349383+00:00"}, "S9ZUdCpb9zaC2UTxpYKa+wq4nVx+eTPoawxhFjTzA4I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\lxmbva6cax-7k4egikr1c.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c4xfwcu9gz", "Integrity": "lLrQvwZnYCDhhSWStXUGrkD91Hlq4pC42G7rdJ8720A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.dll", "FileLength": 9441, "LastWriteTime": "2025-05-30T11:27:03.5329372+00:00"}, "PQKPusuubJeq8W0kZAxncJfQibT+4l95BU9RwSFPE9s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\5qan3ioynj-3pe7a7v801.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Data.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ms0gznr67l", "Integrity": "Ble4qfBANuBrJqJbLX8svklwJy46sFusGmVhL/clC9w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Data.dll", "FileLength": 12436, "LastWriteTime": "2025-05-30T11:27:03.5599411+00:00"}, "z/YM8cOkPfRlQu8dFNmthUBCyj/G/GwKVGpgolfudPY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\txf457tftf-f9c13wi2a3.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jyo2l5h5jm", "Integrity": "JVyM3om4icWNKCxuguzsLH476Ax9eIvxL5dxBeEuxOs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.dll", "FileLength": 9733, "LastWriteTime": "2025-05-30T11:27:03.6529384+00:00"}, "7cAPD48+BtEjtOzzuDszcicKubfNWNsPwrgQBXcBJdk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\jeftphnfft-aznm77fzvl.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Debug.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3gd1v8wqv0", "Integrity": "reaLVeS52Gfiwxs508HbS2di9mZvD2uKgwj01w0Ubyw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Debug.dll", "FileLength": 9697, "LastWriteTime": "2025-05-30T11:27:03.7069391+00:00"}, "XemN/G6TvhizZWCTHiX+4lIgXaC+gGRB9eSd75uhCyY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\p1tdck76q5-7znzv213s7.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h114rjqqy7", "Integrity": "RSX54SWW7Ps+kh72KBzBQNf5nSISg6s4VAQcvzew1Lg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.dll", "FileLength": 68656, "LastWriteTime": "2025-05-30T11:27:03.7809379+00:00"}, "OHPKBFJ2z9s9g1fE1dFp8sPMUcPqpTiYhJpE0tmJHm8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\fcrwrmlafc-snwargkqjc.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "og63oaz81a", "Integrity": "iAzC8GrKVFbnRUWcqf4DFuWNu6lrdtFtFa2+dzLZANQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.dll", "FileLength": 12340, "LastWriteTime": "2025-05-30T11:27:03.1967648+00:00"}, "fqBfAuEdn0LEhcyTxr5dXgqV0X5uADN2dReYSRq1IyI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\4hlnlykvsy-y7e26os88u.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Process.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m4m747x6ak", "Integrity": "OgQZGoeUe3ey/fD/uy+mVqloeorq8d6xaMvqYuW/ZEk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Process.dll", "FileLength": 23576, "LastWriteTime": "2025-05-30T11:27:03.208761+00:00"}, "rM7FWU6vve9yTbx8VLit2JnZVZgw+Sx8EOpzWRjj+50=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\4il7lbrza7-my9znig089.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i8yqawbte1", "Integrity": "LhZT0+tSW9j44PlC0f/ATmgxx6syB+sqnQuhSQqWKvw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.dll", "FileLength": 15315, "LastWriteTime": "2025-05-30T11:27:03.2227645+00:00"}, "FIlMC/PYtKGPxj84SjYEkqYHFtbRTvdVmlIJT/JPo3w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\r0uoyr81f4-vqsiumubaz.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0kqb8e1465", "Integrity": "0JTDhfFqLzTdLQQszzfu6pVl7ZLiRoqb2wet01txhMI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.dll", "FileLength": 17449, "LastWriteTime": "2025-05-30T11:27:03.227765+00:00"}, "/LQ8WXT2ntE3SytJU+8IXIyUbprsvyBUKDimmFaNBKM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\7gzab1iu9d-6nhqtdx0md.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Tools.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ejmpsgm6zq", "Integrity": "pgWKW/8Jg7WUgBWkXTh3pT5xuvHsd34PxftBb8TClPc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Tools.dll", "FileLength": 9611, "LastWriteTime": "2025-05-30T11:27:03.2327644+00:00"}, "QVycbN7ewaKythqLA00mo5zn2ndJiQYqKOrTgk9IMbA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\v2jklyde8z-6knr45w58g.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "il1jhdk1iz", "Integrity": "1hMFkQWzaCdYMzET4CAgHxnwDDPGe3h78HNCNhZxPWc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.dll", "FileLength": 27354, "LastWriteTime": "2025-05-30T11:27:03.240763+00:00"}, "b/EWpFOTHrUJdx5t9eCvJYPcXkAA3AwbyCpEQpzI/Dg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\9c6hutcpzk-vdzmggjakp.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zo7a5ufb8b", "Integrity": "ja22IMiwCz+kyUtP72eGJGannz6P3hL/7IMOPOZULCU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.dll", "FileLength": 9837, "LastWriteTime": "2025-05-30T11:27:03.2487636+00:00"}, "WJ55jS2Ei/Z7KknvUtHCL03twK7t03taqOALZsNuAYU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\1awdiw33i2-wolxwa40br.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Drawing.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bj3p0f9v15", "Integrity": "2TUIqvJawiOpKwQC4/2b5pQlxFYdNS8VA0WfA2hzn+0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Drawing.Primitives.dll", "FileLength": 31270, "LastWriteTime": "2025-05-30T11:27:03.2627622+00:00"}, "oMZfH2qTM0SFF/2gvGkkNUhKgAMxPTm1hr2a2SYTSpI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ebzgiatkbe-og1hutzwxp.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Drawing.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qnp4a1upjy", "Integrity": "Gpee1yA0koide4AzLbSpFMhozpcGJUPoyJSln804LY4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Drawing.dll", "FileLength": 11712, "LastWriteTime": "2025-05-30T11:27:03.2717623+00:00"}, "AyKK7IZXkoBqSlxAgyfeaXem2UhM4LgX2JhvSlgj5wE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\xwsrzg4qx4-lax6z3bqil.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Dynamic.Runtime.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l2lhv7jbx8", "Integrity": "7W/9dx3rc881iAWrC0H8TKkj2nKCTC9uvz2LdyWD58E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Dynamic.Runtime.dll", "FileLength": 9795, "LastWriteTime": "2025-05-30T11:27:03.2827657+00:00"}, "zT2yUWVn73p/GZ/bc/spt+fcsnF9kEvfCAUWm2V23AA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\mvrvmttyh9-vbck47s3y6.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Formats.Asn1.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s7zoknjgsj", "Integrity": "SEhx/C5CCdp3c1eJTKOhLpDeHkvVxkJnOpGFDUrqyZw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Formats.Asn1.dll", "FileLength": 38069, "LastWriteTime": "2025-05-30T11:27:03.2857661+00:00"}, "lbWVWs5kKr6C5IRQ1iR2pPEoIibLsKqk3mbB49QVQHs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\swhxwu4osh-8zsoory6a6.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Formats.Tar.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2ieorkt5in", "Integrity": "ajp0mcxCkMt9V1wpN2pYOO2DwuCQJ0agLjovrw8qGI8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Formats.Tar.dll", "FileLength": 17871, "LastWriteTime": "2025-05-30T11:27:03.2867615+00:00"}, "BigFGSAf4AFLciwSkawjspF+Viapzx2K51pqusDtM1M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\9hvqsh2inw-n14yggzi8a.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Globalization.Calendars.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0aliyv960t", "Integrity": "OIpPkoZ2aMYgtg/kaRgK9rkBsYEXu1Cn9m1ERho2RUc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Globalization.Calendars.dll", "FileLength": 9714, "LastWriteTime": "2025-05-30T11:27:03.2887614+00:00"}, "4xcY0eQjcPC2tllXSukoYEhzvTl1soGwYRy0w5SX4Yg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\k5ymaey70a-tqc3fiyk6n.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Globalization.Extensions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cb1mbohrl5", "Integrity": "2DTNV8AU3U+H141DiNL+1YpwxW3iRc+5MXnH7cMkdPo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Globalization.Extensions.dll", "FileLength": 9645, "LastWriteTime": "2025-05-30T11:27:03.2897635+00:00"}, "51/zIo/w/3WuuDbEWIJYHq6m1PF9qPRy+o3d+nuMwqg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\fsg6wcd6nx-f17e24x6bd.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Globalization.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ziak4n8m57", "Integrity": "S7JMLeCkJl8c9B+DBJugcsraR0lsi98eWlCAOM2SUFU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Globalization.dll", "FileLength": 9832, "LastWriteTime": "2025-05-30T11:27:03.290762+00:00"}, "mn4iFpFBOSKony3w04tmFEMTlaUX3GNpGDQ9NvTd/IA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\hb7avagj1k-kgsrsmpsbr.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cldv3m1s44", "Integrity": "kGFPdWzDvHVZiSoGKD8Pj1/gGqXRlCDI966YcUPXVfI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.dll", "FileLength": 14307, "LastWriteTime": "2025-05-30T11:27:03.2917617+00:00"}, "Z1S0PJua30eHBDP5J8y8C+5XrvGtNESoKhDQMqDRoos=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\xynbs4lc3d-zkvyvoz2ol.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hiedrhaau5", "Integrity": "uYkJCwq4M4lD0mdOfxx2Tps1AZ/8W4GGcrXl1Tl/UmU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.dll", "FileLength": 9590, "LastWriteTime": "2025-05-30T11:27:03.2927628+00:00"}, "L3lak43P9Q8SlF+ciOaTcNRNg3RjIOP4gfqqsW0clzg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\235cf1c14s-by4ytwh9wx.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "05cicoa2ly", "Integrity": "9BnAnZRqaVsqtRgclhRKHW6nx0MitSVlOKKOA39VnAE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.dll", "FileLength": 19081, "LastWriteTime": "2025-05-30T11:27:03.3007622+00:00"}, "YU/1P6SZzkjNHhZfxPpSICJxzXUXlAAR0XutgIRTFy0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\tclnv23oxt-zkgzcnkhe1.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Compression.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kl131szaxj", "Integrity": "KJTfUo+LdRDAa1vZDgdU/TF0Gk4CeAoLa4+Q27ETT00=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Compression.dll", "FileLength": 51206, "LastWriteTime": "2025-05-30T11:27:03.3037614+00:00"}, "sIJ7+4X5/gVON27C3DLTZXP5ViLPVmnd/52sCeIsLGA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\3d0gr0pmpe-bpl4t8oqz3.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "23utfrl<PERSON>", "Integrity": "cnf9mdOJ2Sm5trfULeV+MH52Vapvsce09VXd0U4JRWs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.dll", "FileLength": 15838, "LastWriteTime": "2025-05-30T11:27:03.3057626+00:00"}, "imfUG1ZO7dZRPd0Uyuxxqu/kxKDU4GpJMQTae30F6B8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ukt5mf7pfs-nwwo9qow46.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xec1qds7ek", "Integrity": "GSOtEFeYXJp8bmz0lkhd8S+4F/r/69Cp9Oi8Lbs+ik4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.dll", "FileLength": 13221, "LastWriteTime": "2025-05-30T11:27:03.3517634+00:00"}, "Hxlej2AihGaDXT20S+9rjILdKK2IfPIBbXt6lAtXjtA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\e8gapw5irm-9w17ngo30s.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "i5KMXjm2AtMpLQHWEWM/R32cDhZoPdbdZg7NHWwV9zA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.dll", "FileLength": 9531, "LastWriteTime": "2025-05-30T11:27:03.3977651+00:00"}, "MHatiIJ6uiREx33nxYzDaK8aQNZEGyeWMWrqQ4SK1mI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\vr3o4mrsyh-47l06t5d1j.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ttqfgwnwaj", "Integrity": "kTWyPRfyc7Y1TxZcDad8Gg9a5+r+dj+o2DOWAJepi9s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.dll", "FileLength": 16136, "LastWriteTime": "2025-05-30T11:27:03.4417616+00:00"}, "jrtHI9d6cYw8tmjRINyhkZqTKjlmNPn7lFIWxXSg4J8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\chqi7blkjw-ezfrrmrvtx.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t6f1ox7hh9", "Integrity": "2xCI3TTcVSf3j3yJprTOE3Cow7OfVvGKJygtv3g3BeY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.FileSystem.dll", "FileLength": 9780, "LastWriteTime": "2025-05-30T11:27:03.4767626+00:00"}, "iswHQcsifAVLBcbYJO0vgVRd3xgSdIDf80vNq+5THRM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\mldtna6sfr-kk04jgraw3.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4jzrps27qw", "Integrity": "YIqV9J+UXofxJu0OYJpMlIk18RqxCbLVpsLeJS0P9dc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.dll", "FileLength": 16717, "LastWriteTime": "2025-05-30T11:27:03.5139378+00:00"}, "+Nb2pGPeCrVXX/I5StYWdadWN8RrrSeJDEVCO1YVEFY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8xahdlw6ui-vr6niprlgh.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xamk4lo7k2", "Integrity": "FxqCMee0/NeSRZgswsQ4zCkYl0Qi16/m+hi7ZbYRZxk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.dll", "FileLength": 24190, "LastWriteTime": "2025-05-30T11:27:03.5589395+00:00"}, "Zw3UdLm75ekA/bbL/CnsbUZDhOLaRyRkMOp+h8OM5bs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\nazrslt0ch-y8gh8d0s51.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vlbov39tou", "Integrity": "w4sQ1Bk5jABIHlJXlhRfTLmf1bzBwuyiBh2c9rJPURI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.dll", "FileLength": 13134, "LastWriteTime": "2025-05-30T11:27:03.6529384+00:00"}, "ZxSwp28GddE6mc4Ybh1iW7TKBqzVu2OYw89cc46jUvA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6cjbtyywu6-p0zzh1zvxf.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Pipes.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zykd0f9bxk", "Integrity": "lWgBGbiIGFv7SuP/0/AAxK26bodvjDEBiAnBlIk8H6k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.Pipes.dll", "FileLength": 18712, "LastWriteTime": "2025-05-30T11:27:03.7089379+00:00"}, "65DHbea2uYlaHLgxMmyKpQfIOaRIAhtH13V8N2aB0k0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\7wcx2wrcq5-x1i445epty.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8sq26jzl8n", "Integrity": "GFbkMBpKtSFqY+Sj2OsawSFHjIVfbmW8DpkGPecQUok=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.dll", "FileLength": 9716, "LastWriteTime": "2025-05-30T11:27:03.7829379+00:00"}, "9pdEoPtwTZ8dNrB1Xq4z2jfuA5Wx8LAvSI57zY47x7U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\2p9kgt7ozs-c1lj5zgilz.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qnchxfa328", "Integrity": "quhSP5jRpx+9ATNtZCHrC+TjgJdvM5OZyOGdDm6DoqI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.IO.dll", "FileLength": 9740, "LastWriteTime": "2025-05-30T11:27:03.8049391+00:00"}, "nDj/UXFbLUcXmFByWdG7dnQVU3rGj8Lyky0X7C/HA38=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\xvheuxcvg7-2j2d7bs165.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Linq.Expressions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "um4hglhqmh", "Integrity": "SrpdS0IVLgqrtG0lWwwr2flTcHKhwyy04yv3XNRlGHM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Linq.Expressions.dll", "FileLength": 223501, "LastWriteTime": "2025-05-30T11:27:03.8359368+00:00"}, "NZbc0JQWucIz8ius2LKTbiEQ4XmN31ysuexcg1Sb7Oo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8uaag7pm04-y9x1dxbxjq.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Linq.Parallel.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w5dr1bxbqt", "Integrity": "nAeC8ADzjWy65hpWSR3y89Gc9S6gdVxPya//qCgHpNs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Linq.Parallel.dll", "FileLength": 96127, "LastWriteTime": "2025-05-30T11:27:03.8259394+00:00"}, "qSrnNIL86Y616qhga+kFOAusVqck2fpuGsrxFD3M5F8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\50xlahu8xs-fuh0anxmog.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Linq.Queryable.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mcs751l4j3", "Integrity": "uluLQRMTQNPT0WlFCjzfwAIQuUc9jmBhtI2efc5uMkw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Linq.Queryable.dll", "FileLength": 34027, "LastWriteTime": "2025-05-30T11:27:03.8779376+00:00"}, "QB5NY153jxTOCRzqUds0XCrkXCcHQNvpdkfedvaIKh8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\30p2amsy55-puq6i022xh.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Linq.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i1kqf5d87q", "Integrity": "9lpNKB75Kv5JLmKgQog/007IOuFExFwQK8rRl3yprYE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Linq.dll", "FileLength": 56167, "LastWriteTime": "2025-05-30T11:27:03.9439393+00:00"}, "bbcRzOhk8whtbFQl/jj8Cs3MbFWFDLIpSNCpi9oT7eE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ujcudecg3t-ysaffd1xa1.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Memory.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p8lrz<PERSON>ldia", "Integrity": "t7ARIdHGNKy19DmSaHGc0p64AE3MG98XzAwfvwrUKpM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Memory.dll", "FileLength": 28475, "LastWriteTime": "2025-05-30T11:27:04.0249361+00:00"}, "HB6qOhUgeDeNL71y8QqVUNVESkObt/6eVrpMcRTDC74=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\5obvfkippb-0gv9yrjrmy.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Http.Json.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fsizfc5m3w", "Integrity": "3zjRuQiWlhZ9JmhGwVG+tJZXyEJU8HZiIw4GITvwesc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Http.Json.dll", "FileLength": 22564, "LastWriteTime": "2025-05-30T11:27:04.0569403+00:00"}, "8U5fxXxDCbXZZ1v1Z7iq2WvEIPbqMqX96vVWGmbE3yQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\pxrhwzr2or-zuctlxzhmk.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Http.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtdvzw934w", "Integrity": "tyqymKgHZutouzGz0t64AspFuuD3SBahddHY3ikgqYs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Http.dll", "FileLength": 115583, "LastWriteTime": "2025-05-30T11:27:04.0969395+00:00"}, "e++gomCevWNMPmLbh+rM/nktFC2GD4dTIXO0NKrY4Yg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\i18zg7iw86-u9unyi2q8h.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.HttpListener.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qnwidgc1v4", "Integrity": "Kh0pGzGfvO6oN0yzF6GR4G1hfYm0jsH9ebrcBSc6+B8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.HttpListener.dll", "FileLength": 23650, "LastWriteTime": "2025-05-30T11:27:04.1399392+00:00"}, "pKQhfDP6U3Mjm4MnDPmU3s3kyg9e37mR8JBBpxhIMjI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\b0hhjxvn1s-ofahbp17fk.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Mail.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "isz9ipytpx", "Integrity": "jiz3uiD2E5bVCaRkwVqz5NJwOSna4rH7HEhWsMHaL9g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Mail.dll", "FileLength": 49803, "LastWriteTime": "2025-05-30T11:27:04.1909391+00:00"}, "TyyyjZU4e7dw6w5mu2Po4PMPCKKBiKtu7FMTWGcYfaY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\w4r54ppl6y-egcufd8u4h.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.NameResolution.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n1fkahrn6c", "Integrity": "1dh32L0CfBGFtqp5fczG7gExvvuH7qfnG6epvHWRr9M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.NameResolution.dll", "FileLength": 13297, "LastWriteTime": "2025-05-30T11:27:04.2209435+00:00"}, "hMZE2A+IYnh3A66hlXNWlkGRVf5iK8ei4LA2lem8hcA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ni03nv6d09-7e6a6i7pq2.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.NetworkInformation.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "crwmbvw4wn", "Integrity": "WBY7+QioazU44HK+CJ4DDntX+edRoJ6Mc6FoH9sncYU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.NetworkInformation.dll", "FileLength": 20228, "LastWriteTime": "2025-05-30T11:27:04.2449407+00:00"}, "LX6TPndOG8sYTM89cETRXWRdXleT3qe9vv0wWC54VRo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\vpkvt5hkov-s1b99brmzc.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Ping.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "swrvohmatm", "Integrity": "9IR+tsrLZwDvwGRaFIwEO8FmDfdTDVrLV/1ulogcqTs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Ping.dll", "FileLength": 14610, "LastWriteTime": "2025-05-30T11:27:04.2649396+00:00"}, "13s0+HaD9AaPfWvma0wFjBGBYm60az+827iu3pTxqlk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\4ec7opzr0o-8v6y19yi1p.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h6aqnwzdmy", "Integrity": "CR0bmsnxty9Ek7KGLex2PRWONvpCs+ms9Jat2M2yXmU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Primitives.dll", "FileLength": 50141, "LastWriteTime": "2025-05-30T11:27:04.2969397+00:00"}, "etZgoQ3vMO1Glq7uPhgqaBMAvpuhUwNhOcMdFwAjZ48=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\65ccme371t-utcn6025w5.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Quic.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t1lu85c25r", "Integrity": "77113OujX77nhyWHT+fu3Dbh49XtYwwADfdzQUP3Aww=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Quic.dll", "FileLength": 18770, "LastWriteTime": "2025-05-30T11:27:04.3199396+00:00"}, "eZuohYJFMi3Ev0TAI3yVebJyJ+yfqW4E/sQRZla/FDY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8ett36mefj-sse8d506ul.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Requests.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "57aoqbdd3z", "Integrity": "ob+MN4D6vdl1RM0rHSXtGKq62t54BHLopu3YzMRhdkg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Requests.dll", "FileLength": 26271, "LastWriteTime": "2025-05-30T11:27:04.332937+00:00"}, "9fNzT4J3DMvgouI7LfuVKnCCxxmYO1D+fbXC7qGbIiw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\gnkux69fl9-ocjcsxyqq6.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Security.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2cicip31sg", "Integrity": "LlJILawxdoTP6tP+OUOpXGl0PNo2aG5kA1SFu7sGE+0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Security.dll", "FileLength": 32814, "LastWriteTime": "2025-05-30T11:27:04.3659375+00:00"}, "3JLrpOEOU+vKH7rktf+S1Sn6QDCmP/L9mI/T8+KMkCg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\utuzu0mrle-gu2xmfmbm0.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.ServicePoint.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ohm1jhn9ry", "Integrity": "HK/qyrgvPbNy9mXH4B7l4ZE+CxFBbuavzjbT9d4jZFE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.ServicePoint.dll", "FileLength": 15023, "LastWriteTime": "2025-05-30T11:27:04.3741791+00:00"}, "l/LGUQ+nYQS1hgkwEhdzVpAE5iZB+XBBcI9Eb4fxvyA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\tyavw5n7u8-83233hlgeo.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Sockets.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3zpiujadv", "Integrity": "FEi7BfUaz8nwz12VWyMvI31LUXlAkXu7Ri1Irmwp8a8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.Sockets.dll", "FileLength": 30727, "LastWriteTime": "2025-05-30T11:27:03.8879379+00:00"}, "tYAFaPZwGTGqBMSXQH3EwRtkL7OtUKY7bWFxeJxHdNE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\yw58x8e877-anrvcpiuw1.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebClient.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yt1losh608", "Integrity": "emtqHpAFeSKin+SPJ/E3l2zfj90b3tuZN1u4bdYsvt8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebClient.dll", "FileLength": 21040, "LastWriteTime": "2025-05-30T11:27:03.962939+00:00"}, "B3mGI6me12Sfuqsxmj4A8v2GHH47PdDjKrzuvFCXEWE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\t7wen7wmdj-j4qmfq7fqb.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kri2cdys8d", "Integrity": "gDRFxNe2gHGtVjK0Kn+GhNCM8aOKFU19hOdnGH7pM4M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.dll", "FileLength": 18409, "LastWriteTime": "2025-05-30T11:27:04.0139383+00:00"}, "4+GZcBEy31DI1hCICgE6OQiOz8gL1TzkG6j7VxQCu8I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ybyy0dv30s-ns951ngf18.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebProxy.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kvyup87xuc", "Integrity": "EJVpwfyD1tY5O0tys8U9GpKZemLKxSDZZ6Df3N1Bf3E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebProxy.dll", "FileLength": 13284, "LastWriteTime": "2025-05-30T11:27:04.0469372+00:00"}, "r7LurTRzXb9hL9xTbzUK6DfyUfNRseHbiUb0U9UwfOY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\wl8dyj1hk6-d61i1lavha.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5gctno93el", "Integrity": "u/QJnTSsZXqS21KG71JOGj9ur0LCsFdDJhxEMaVwDE0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.dll", "FileLength": 24215, "LastWriteTime": "2025-05-30T11:27:03.1967648+00:00"}, "aeakI2BiNS9x5Ee/Ff2urIJU3fEAoFws4gtR3GANiUY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\lcx0zku6es-bh31g7lpxg.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebSockets.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "elii2j09y0", "Integrity": "XiODYOAEqHzIeWn/iGw82gOI5LFkQdxEP6Ps24KZJJ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.WebSockets.dll", "FileLength": 39418, "LastWriteTime": "2025-05-30T11:27:03.2117623+00:00"}, "s3kKUuTdlepXI+S+OxLRA6PXGOQIwwTo3P+3WzqG3Ew=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\n8g3qu0pre-csalat7lcb.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8ry3ybtxi", "Integrity": "bVkbXSYKqWUF9kbdJLOixOUgpJkQY3hHsz3Kd0iNv+g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Net.dll", "FileLength": 10497, "LastWriteTime": "2025-05-30T11:27:03.2247619+00:00"}, "eG3m4DnV1ofhpZJhLtBjVhifW3/CSHxO5DQPymJxYGw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\iw7e6nox6y-48aweybv4d.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Numerics.Vectors.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bbsjwkcupj", "Integrity": "F0b847dfaPVpsxEICrUuVNnhsaw7zpcfRRWtf2nbQBg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Numerics.Vectors.dll", "FileLength": 9600, "LastWriteTime": "2025-05-30T11:27:03.2317647+00:00"}, "DGV77qP+uPh8KcrAeAqwS3KivDflFAGRASmr8VT+sgA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\zto7gctzu4-o7y0omx6a1.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Numerics.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gndhz2mpyz", "Integrity": "pLXfLMictWGDshDx8XcfG7fGprgfwInsYjwCs6WolcQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Numerics.dll", "FileLength": 9779, "LastWriteTime": "2025-05-30T11:27:03.238763+00:00"}, "i8kivjONG54uzMo0XkkndA7K8zn4D+ydLHm5aPJi9B4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\kyufanmt3h-6owajhnn6j.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ObjectModel.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mb7mmbk9gb", "Integrity": "kjACPMdat6czZewSYxLnN5gds7u/jYpQECyJR6F2FS4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ObjectModel.dll", "FileLength": 20635, "LastWriteTime": "2025-05-30T11:27:03.2437631+00:00"}, "FfFcuETizuGEPrRARWaDYFKMY69vnRkp7QfXAPeOgzs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\xo9uqp0jtl-6u84u4l1ob.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9kbuwfrbdl", "Integrity": "CcLN4yJJ4h0RxRCkEXbNeoH7faXNRkPCxGwK7kH0wKU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.dll", "FileLength": 310747, "LastWriteTime": "2025-05-30T11:27:03.2697616+00:00"}, "fgXT0haUuM4eCeQ0A2O91LauMCppT9gPEn5R1W8TR3E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\e689nchqnv-rs7tykhnbs.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.Uri.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gx0vwwk6dr", "Integrity": "jIl1ZrnTQu/eap7SaR4qKCQvrOFfJxIdKozFZ475NDc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.Uri.dll", "FileLength": 49035, "LastWriteTime": "2025-05-30T11:27:03.2987642+00:00"}, "dMleq5fNwnQCcq4caEQvtwnyMicDN1Mk6h8fgHjB8q4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\puk8nrxb0o-8iqmhl5x7g.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.Xml.Linq.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0kcithkr0u", "Integrity": "EDGvqw8yX3xwl7ZjePhhrWMuH/uGnFlP+mf6m2m4uvA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.Xml.Linq.dll", "FileLength": 67506, "LastWriteTime": "2025-05-30T11:27:03.3127631+00:00"}, "eunOQl66LxrSTjafA+cV8uqTDk1wlEGc25C+5sOxUWw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6n8rchswmm-vd428t4ls6.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.Xml.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wl9ee1nql3", "Integrity": "3HWmcXL4egeYm6epjDBZRwLpPc7Wp3gq5kBTPf5VjiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.Xml.dll", "FileLength": 1086110, "LastWriteTime": "2025-05-30T11:27:03.4277607+00:00"}, "qy2wt1PuH/yQsHzHsxYABF7zUB19k7l2H1EIUu6LM7U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ng7v1c29wq-0fx71p3w82.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "37t05movx9", "Integrity": "zxBz6LaY3Et5TzR+Hk8pGj7sMGUuB3H/eZR2bEuZo+U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.dll", "FileLength": 19989, "LastWriteTime": "2025-05-30T11:27:03.4607605+00:00"}, "F/SEhoGNYItWgS9NnAJqApszDXmezHCyphSEw/wWiLg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8kom0ba6sr-2h3w2ob82f.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nf7sa3hh7p", "Integrity": "o5ohGwS/yRFVH1eYpYduBVxDoO/RyYZLDzuOlF87c6c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.dll", "FileLength": 9744, "LastWriteTime": "2025-05-30T11:27:03.4809293+00:00"}, "eG4zrvPf59ndQa2uTxsEyPBZMLhySvMoDig34XbHMn8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\7rdv96mdv5-fe5m6ra5ie.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "33oskqs20t", "Integrity": "7+SPn+X9eK0cLx6V0dRqUnBP8TVNEg0V905w9UqpqcU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.dll", "FileLength": 9690, "LastWriteTime": "2025-05-30T11:27:03.5229418+00:00"}, "ZZoeHhOomyF8o21fMkihSvsDJprVrGx63DR0H9Nwmrw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\37p2feq6zk-axzwfo66pj.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Emit.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4w2abnnusn", "Integrity": "JMSDegEvWloF5o3haFfQFhH8BLNkMk9plcsJLLBg3PQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Emit.dll", "FileLength": 9644, "LastWriteTime": "2025-05-30T11:27:03.6229385+00:00"}, "32j0LZsWd6ub5oHi3o8TY0z7XEbl8AzQHdRn+n69Kg4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\l3e81v39si-o5m5tdvr3e.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Extensions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1obkiw9gm", "Integrity": "elVd8c1gvqLzJ7ZfXpIrNV2IBA0e+54rWbgCSqFKgh0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Extensions.dll", "FileLength": 9502, "LastWriteTime": "2025-05-30T11:27:03.6819395+00:00"}, "6TboRCiL8vp8Ttzo3VeWhg99CRIrAr4VP7s1VC+9Eds=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8vukkgmc46-6xbff9utkz.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Metadata.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pjt8rofxt7", "Integrity": "ay3lskVxx62H5FXlLlo0v8k9sBreRPV4Kc2SkST/Hyg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Metadata.dll", "FileLength": 185631, "LastWriteTime": "2025-05-30T11:27:03.7799373+00:00"}, "CUkO/vYzqSOHpSk4JUEnsaIzwnQCL46H9lcNVko5gwA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\80x1btp9tt-o35oix5loo.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t2bseo3i79", "Integrity": "6/ost7C8xzzUK+UD7oC5CUZEVmN9te2C1U6uYSyp9os=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.Primitives.dll", "FileLength": 9846, "LastWriteTime": "2025-05-30T11:27:03.8209409+00:00"}, "5uNl6qiDFH4ZOXwaT7NOE+oCmq+1GSCp659pZ9M/2I4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8rfg1w5zee-fz2kvw5437.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wqemltgcxh", "Integrity": "2EJgVIGFB4Kev9kU67Ro+vHTcUXAqeBDF/ghnpPucc4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.dll", "FileLength": 13512, "LastWriteTime": "2025-05-30T11:27:03.8439372+00:00"}, "2uDYD/jo3YV5DKm+jAy7Hydr1ZamlXH96+SbS/F0T54=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\bhqp8iyxp1-d2sd890z8e.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wdon3n2ozn", "Integrity": "zGXqkzr+Y/iUm7fz4lzmJab9MJnQoDdXVPImBiekNL0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Reflection.dll", "FileLength": 9901, "LastWriteTime": "2025-05-30T11:27:03.897939+00:00"}, "wG6/xpMvjF8dLF94Wr8iVXXX7dVt5kzGMlgNXy3JhWg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\1fhwtkok23-731h7cefq1.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Resources.Reader.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w42ktfhtv1", "Integrity": "0HAVlDGX60RUM50dnWIErbVennHtVlTI1ewqJJaAoMo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Resources.Reader.dll", "FileLength": 9460, "LastWriteTime": "2025-05-30T11:27:03.9769366+00:00"}, "7O9e3cBfLjPnhFU+6iDIIHkqeUSC9y3ELFq4dBGcaK8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\pbxzpmd6i3-wzm1b8ot3y.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Resources.ResourceManager.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pokue37frg", "Integrity": "x1EJCiFHVYbXpE9eOH+whvRHTM2Q9BeCjYCUu2V7ENU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Resources.ResourceManager.dll", "FileLength": 9777, "LastWriteTime": "2025-05-30T11:27:04.0219364+00:00"}, "ny3CcJz40gw84tiYgM8ucK30in5RdLFD9TaJiCW+hOw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\48iikxhyfr-plp7lgigcc.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Resources.Writer.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ooqtbgh7kd", "Integrity": "qNj6ojvsLXblnwQkjueNFGsvGDp2dpl6x6Am4T8wIAU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Resources.Writer.dll", "FileLength": 15599, "LastWriteTime": "2025-05-30T11:27:04.0499383+00:00"}, "joHfRPgRZdt5U24b/s3+bJyge9ad36W0ylCk0oH3AYM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\3dw7fahs1m-iqufve1780.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4urk5blpmr", "Integrity": "FHU5NcDSPc/ZUAlQFBH3fZnBABN+1Ymn8J4xrVbrriI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.dll", "FileLength": 9525, "LastWriteTime": "2025-05-30T11:27:04.0799391+00:00"}, "YCmZ0InD/LDIYmCj0UDb/0W55golZMrP1XASNsQxENo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\f8ag1pxu30-kis47j3nts.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7lw33tqi5", "Integrity": "alLhdeHeQAIZsmu1jvFXTdDyUKcyf8mHKrA0S/p4KJ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.dll", "FileLength": 10693, "LastWriteTime": "2025-05-30T11:27:04.1239381+00:00"}, "mWkG/UeGUPk0IpeXgxnXdkplRT53NzZgY2pX88ITpRg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\xrv645ho57-slf3ufvszb.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Extensions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dwwmuaf9wc", "Integrity": "8zlCbkGM7dTfUOL0VE2Vbvb//2bxERcSBcU3NlOggto=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Extensions.dll", "FileLength": 10484, "LastWriteTime": "2025-05-30T11:27:04.1879383+00:00"}, "Gdv0VAMdgoaCwn1Pr/KqpLEWhMUYAbmno8GM/ZARF+E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\3z82uwpi6p-d0fhf5pzvk.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Handles.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bo1va966v7", "Integrity": "xfe+Dgu2KKj/pfJNngpcy5kCUdrK6HdCxn3IfHiNjaA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Handles.dll", "FileLength": 9689, "LastWriteTime": "2025-05-30T11:27:04.219946+00:00"}, "BTkJ4j8Z8m+J52hBovbFn8G5ciptQTMyzkgwWNiiQfE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\oz2i00mr7k-3tfiukpww7.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l6p7f9l4zl", "Integrity": "Pf7xhoYxxUkwUWhErwWJxOywD5X6KHrylJ+dI7cLsXM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.dll", "FileLength": 39987, "LastWriteTime": "2025-05-30T11:27:04.2449407+00:00"}, "Dl8a6SutF9jtyt5wzqmLLEQSz70zGX+5FRV5qO22wYw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\luhcrf9u60-gaq3a7ppd5.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7s6jf5yl1s", "Integrity": "6VOAwrayX/XnAvTD3mDCjKUrMBUK96ob4/J/KGZPGyA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.dll", "FileLength": 9629, "LastWriteTime": "2025-05-30T11:27:04.2579376+00:00"}, "Jkuhg+iJ0Be6T6atcVzERp4Gr9V82xYoX5MrQB26eTA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6wafdatvnk-y4n5e3hx86.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.InteropServices.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xdv6q32xay", "Integrity": "m3XagH83dB+SppBiq2hfH4H02rLqsDKb+O5h9Wf+bS8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.InteropServices.dll", "FileLength": 20546, "LastWriteTime": "2025-05-30T11:27:04.2669383+00:00"}, "c28affXMVQ5apPPO0PBVnePMdAUwp6MT3zVcjJ1LqP0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\j0snpxg21h-em2mfj48ep.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "11qndkkyia", "Integrity": "e1/xh9lGnbfzQ4VThHL4pjQEhF9apOgm73Eopt6gC1Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.dll", "FileLength": 9897, "LastWriteTime": "2025-05-30T11:27:04.2809461+00:00"}, "4sghIsldvLdbRwfKCeBliuM21r1r1P3MVXGP1RlYA+I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\9didj28ql6-qmwmkhetj4.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Loader.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "64u42vbrhq", "Integrity": "igAAbL1eujDPGgk9WnM/Goda1UrOqrYMoTyiQDGzHQw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Loader.dll", "FileLength": 9805, "LastWriteTime": "2025-05-30T11:27:04.2979394+00:00"}, "eeIh8U5hEXHQ7IOl1DfuUAoDxDXmSmWlAQSOd978uhw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6woj0f9xfz-6tmuj37m0o.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Numerics.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x7cx9zej60", "Integrity": "4461VOYt/zi/5Lznnrv2AGSHLV7O+OIR3XsWJyLE+QM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Numerics.dll", "FileLength": 53842, "LastWriteTime": "2025-05-30T11:27:04.0749369+00:00"}, "m6f/jZ3018lPV+DE0rUidbhkUNB8/O0wew5azgFDB0M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\f15ohtpxq7-rvczkzevdd.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "48g7djtqdt", "Integrity": "HAnrpga6AO2f0v06Tvl2s8OnUyZ3SIAo6qA2V7S5Pow=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.dll", "FileLength": 31984, "LastWriteTime": "2025-05-30T11:27:04.1189392+00:00"}, "uWJFcxN1lacqFSR7bQfAI3DBIXz5xFx6JJsieFvsgX8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\h0cj061vzt-mm1od2xvkp.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2hya3y241s", "Integrity": "+me5RVznfQXuWO3k+ET6PyB9nzQugIyuLxKm7mMnCh0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.dll", "FileLength": 9709, "LastWriteTime": "2025-05-30T11:27:04.1729409+00:00"}, "8/pJdwvrCC5krr4/luOWqnGQBpu7HulcKxaTm5KjcwI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ltko1yz3q9-um193sfy61.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tm9bq62hsc", "Integrity": "hZc+6ytCc0dQIIzI7RlLyjpvrRz2verPr2Qxy+Gxe+4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.dll", "FileLength": 13028, "LastWriteTime": "2025-05-30T11:27:04.1869389+00:00"}, "v1zDHcppGbp6QqZOyZ9k5kPvZohs03DgnEbIEboAbds=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\u6jwhw3sww-yqeyyxiz76.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xfo16ts02", "Integrity": "ZlBphf4dR/TbFwnYfo25zBX8BuZPLBb6GJhC2oli8+A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.dll", "FileLength": 10072, "LastWriteTime": "2025-05-30T11:27:04.1999371+00:00"}, "Q3s00DvZ2nSCzACiZaII/cN+kuNBl39Ufg892ygfGgg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\mhj9zh7y9g-c02fhj7lqr.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "69kt8an12e", "Integrity": "yisv7JWn4+YKtfjbl9CZk1qq3rSH8Wpy828JiqveAT4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.Serialization.dll", "FileLength": 10296, "LastWriteTime": "2025-05-30T11:27:04.2189444+00:00"}, "muGElj+GmqOysJfeILfDoxQ3dA8ftXIQuchjrZNZwaY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\x1dsa86h2l-ggbzj4oo2p.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2n4chwjyy5", "Integrity": "foMJPYRCrhEwTKFNvVC8Mt9kOBmvK/SrmL8cP8A94+Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Runtime.dll", "FileLength": 17889, "LastWriteTime": "2025-05-30T11:27:04.2319371+00:00"}, "NNYRijhDbl0duI8NwG5f/jAvZgL0ksE5+AuU2bm4IeQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\qi1dd2ulsa-rzj4akaqn0.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.AccessControl.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w4n2einr0r", "Integrity": "oGcPvldEz3Q6IIhS8s6le+EavyUV5Fv9WmnRzRW56aY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.AccessControl.dll", "FileLength": 24265, "LastWriteTime": "2025-05-30T11:27:04.2419391+00:00"}, "J04TZ+JJWqpIy3sM32OVlvsrM5vQBybxu4JBXO/NadA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\nbwyg3vd7f-vb5e1tpwtv.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Claims.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lihjwzbl2i", "Integrity": "bVuxZ8VAKQpdfQtA75BVIxuH/Z3RrOCdp0XXEW1sfJg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Claims.dll", "FileLength": 23106, "LastWriteTime": "2025-05-30T11:27:04.2519395+00:00"}, "wS7kT8OyjYiWxyCGz/mXMqX6hRtn6hSl6fuGtcu2Zmg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\zt8pdbaqgw-otyuachow3.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mob3b7g243", "Integrity": "5kS0tjEDdgxE7QrkM6vhyK0JH0fY64hKBg6Olcp+O+s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.dll", "FileLength": 10230, "LastWriteTime": "2025-05-30T11:27:04.2619501+00:00"}, "lvdQjTL4ntUUayG28cAcmZbhMYmpIQ02bE5mPFeV/f4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\evlcd0jyff-29pyietgj8.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "twowyj5yfg", "Integrity": "HaRXwB5S1utJXElfGs45YGCN+yX6t5FS4ZPiM9byQO8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.dll", "FileLength": 9999, "LastWriteTime": "2025-05-30T11:27:04.2719374+00:00"}, "omYy7fqTQ3Vt/dMbsvKhDutW5qc7VSLKdepkkvRzieM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\quijo7rd3i-2xv12hum3k.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7a33racb7v", "Integrity": "Xxa11MBTzcdI30xtBscQFV3nq2GqPVNwPbeq+YvJBXg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.dll", "FileLength": 9940, "LastWriteTime": "2025-05-30T11:27:04.2849393+00:00"}, "rz+l1zLdmd3G7XAQq7lizp2TWEAURPlGaWBa0KfV4wA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\6bm0ll062d-t9bjlnepcj.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "csxkx7evpm", "Integrity": "9DqL93Iiyv/N2C+4vR4Ce6zS1HiKF3dAx4FKoqRFcu0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.dll", "FileLength": 9788, "LastWriteTime": "2025-05-30T11:27:04.2989419+00:00"}, "vqVytc9EIvPnn+am8hne2+TgJ8i3DPf/BIbWIWgNbMo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\cu3u55jfvv-m5pc6imm0c.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dw5y25tg0k", "Integrity": "o2dmXK+SDjbAxCyPWayi6IgfhEvpaV0ejOXBg2HFRf0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.dll", "FileLength": 9755, "LastWriteTime": "2025-05-30T11:27:04.3279388+00:00"}, "YDm4Jq1IvcDVjH5QBgMEyM7owZHl7G+aktkH0G+yfTU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\jwse50c08t-29ez94zphf.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pzrvi9rdjd", "Integrity": "i1rFZ/iF1ObQ3C74mv2eJSgLKjpF/v310Vh+G6bwcJc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.dll", "FileLength": 9826, "LastWriteTime": "2025-05-30T11:27:04.3379406+00:00"}, "o5Sz8hozmyyPeIAxgGMcoAaUYHKjB1H30NDm5e1Z2XA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\e45s3on5mr-p0eob9uhw4.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kyuto7e4ah", "Integrity": "/LWAdAUaL900i1CJqFvWE+M+Xy205jusBXpxX67JWdE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.dll", "FileLength": 10213, "LastWriteTime": "2025-05-30T11:27:04.3449409+00:00"}, "xPoSjufjpUGqGqXK4fC9bWGfd0anLBQvfmBBv0OTW+w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ehpzbmjm1o-rxk490hjzw.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a5tije8yhi", "Integrity": "8rAAXcXd0+HeZkk8z2xcYvvdEWrdnzoxEMZS/mqIZuc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Cryptography.dll", "FileLength": 179785, "LastWriteTime": "2025-05-30T11:27:04.2699373+00:00"}, "nApqBkNnmun8hGEZ9XZWN+lRrm5ECPSEljoe++hAS5c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ez0p456caz-k29s2fe32y.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Principal.Windows.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4zzu3f8uit", "Integrity": "oJCclB5jSb9Kw5tNpa7F82iM0aAsaFv+p0HGJwkZ1Iw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Principal.Windows.dll", "FileLength": 18736, "LastWriteTime": "2025-05-30T11:27:04.2989419+00:00"}, "rCmgJ58cK2rkg2tzaEO+C7laDKkANLZUBCT/k15KDHM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\5w1j4ht1zr-oshomkmf42.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Principal.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8vg8jb3j1", "Integrity": "01l1eIVUxoPmMn6vw0NTHnro9Vq0T5aOcFvSD6ei/BE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.Principal.dll", "FileLength": 9716, "LastWriteTime": "2025-05-30T11:27:04.334938+00:00"}, "zdjl36uH8OmwSP/wtjWaWQQpz3gi9Wf+c6GfaVPoTQA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\000j7hmcc7-5umgdvtfjt.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.SecureString.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j88u2fs4u0", "Integrity": "zdyKchX8uLWquqoVcdTO/Ickra8QVQ0LeTcpcGzpxLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.SecureString.dll", "FileLength": 9675, "LastWriteTime": "2025-05-30T11:27:04.3609391+00:00"}, "FC1e7eCxmJiQ0VsVDcocV0tqMAlaX8+PLdbYKDDcsRM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\avgjnle1nq-og0900zy5q.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "029b5a73dd", "Integrity": "aED4jlok/m39KyS4w/9H3s6xhJuADQ4QPoEx75/I2kI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Security.dll", "FileLength": 10711, "LastWriteTime": "2025-05-30T11:27:03.2027623+00:00"}, "pZZwYaCDPGkYp1eAOM4wMWa5BadBruxhKWbYm3WS7PI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\grv7el5snz-pm2x8yto7h.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ServiceModel.Web.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1j4kqldccf", "Integrity": "7fFOZsxkD75MniUIpkJvL0iYmhREAiqec6fXpF55HMk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ServiceModel.Web.dll", "FileLength": 10272, "LastWriteTime": "2025-05-30T11:27:03.2267628+00:00"}, "f6GAj1Ac9t02gGGK8c3TmXSUqEq0geN/Dt9pdlrMOF0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\3mxu1umy26-7rlgq1jzhn.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ServiceProcess.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xkwl6u04gs", "Integrity": "z2m+ORdx2Hr25vGwiHjzvkFuPZl3KaPazIo8OesXKk0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ServiceProcess.dll", "FileLength": 9880, "LastWriteTime": "2025-05-30T11:27:03.2377628+00:00"}, "WtYkV+lW4XrlB6MlIAD/JEp1qtenjn/C+B0n7aUfp5I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ku33xnnebp-lly5p86j9s.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oyr5jidtn8", "Integrity": "MFitJ7uH01alAtHFYpQ6YxMMoOBMebfa7RwUBF56118=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.dll", "FileLength": 525395, "LastWriteTime": "2025-05-30T11:27:03.2897635+00:00"}, "25TXBdiFFmglqCCTQN6iiY0Ljvyygl+rFeGYixXILIU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\gc5scz0900-m1fufjztnj.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cs806tak0t", "Integrity": "fCYGRbgFe35Id2XEFeKRooqsEvPdVQvc6D9pR56QouY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.dll", "FileLength": 9739, "LastWriteTime": "2025-05-30T11:27:03.2917617+00:00"}, "EJrDmIZmdBNAlMy+IRqBkaQkH9heG6pS0a0b5CQneBc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\3ulzems4if-nch20q1cx0.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Encoding.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "93k2e4y3iu", "Integrity": "NRBIOcjha/1Gn/9TNX39mQoDs3+KY6MDw5xNMF28kE4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Encoding.dll", "FileLength": 9778, "LastWriteTime": "2025-05-30T11:27:03.2997622+00:00"}, "zhYnJD+ajXwkGjYx43nLMtZsROWV16pm5LyfBlu1HFU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ujctosoxx3-g3nf00krc1.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Encodings.Web.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ww76idinhg", "Integrity": "cAzcU3jWkT60l+HHxPXLukC7HbUKJG3MHSv0p74Nqrc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Encodings.Web.dll", "FileLength": 31856, "LastWriteTime": "2025-05-30T11:27:03.3107618+00:00"}, "bhsueBkvl1aRWOAMNAUt/AbWSdHVSGR2sQK2g3MjLgQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\mwyjxpzvxr-7fqh1mn6la.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Json.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "htlrase2st", "Integrity": "NUFNeMNr2Rwi0aTIjEyFNcQT/Cyn4kd4WoxVuCp8LK8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.Json.dll", "FileLength": 198274, "LastWriteTime": "2025-05-30T11:27:03.3227601+00:00"}, "Mr7ft6C+W0+Vs+hxifdCCXgm25M2o6t33jbSQq25ymg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\5t1jpky0mf-9spyedkhb0.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.RegularExpressions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7n8bmm0lsa", "Integrity": "fdEqkV1lgSLo288r8JpGrzBvwAyEIUYoqW1tXyOzoes=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Text.RegularExpressions.dll", "FileLength": 156013, "LastWriteTime": "2025-05-30T11:27:03.3607618+00:00"}, "Xc89MyIRLSL2Gw1TDQkJZeLTw7SchPvzjjxPDX/p6Rs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\xwpr7lba56-iusbqm6v0b.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Channels.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t5o4t8t1l4", "Integrity": "rz/44KZCefeTtOjz042YYjbygc55xb+7j5E6xmZ5aW8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Channels.dll", "FileLength": 26675, "LastWriteTime": "2025-05-30T11:27:03.4097609+00:00"}, "NXfe9ySkgtT+vyhvi49rHkNec5xd5ixkcNfQsClkArQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\g6bng7pe7i-9ptvx1bn39.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Overlapped.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7wuhoq91ty", "Integrity": "WbnseSq6cs/gw4PTCm6qXhbGiHQTqqCveH3pT0+2Yug=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Overlapped.dll", "FileLength": 9800, "LastWriteTime": "2025-05-30T11:27:03.4407617+00:00"}, "MeZXjG26Bogpa8apHgvkb0o6ewFjTsYByc2jHK8ECuM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\de782yaja2-qr4v0c1t0w.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gotaqe6a59", "Integrity": "wOZYxlhHmkF8YSWYuEJEZ/jgNcAAyJ4Dkaw8klyZfN4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.dll", "FileLength": 82654, "LastWriteTime": "2025-05-30T11:27:03.4819377+00:00"}, "CKMuGBNhZJYhK7RLxxd3J5da3sPCpPmQZN4si3ptqAc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\a70c1nc4uu-9p9i5smpye.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bav1fc3543", "Integrity": "XRwKt8cODHO82BRSJQxsJo7J65le1jwrZMjWklywS+8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.dll", "FileLength": 9866, "LastWriteTime": "2025-05-30T11:27:03.5239389+00:00"}, "Op/jJePG0VpGRU4GbiWUReZ2KqaIAbRrFkPcON2UW/Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\vc4pskk6tj-83e12psy51.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9wxkgvb9ql", "Integrity": "ZzupW2EyTgioQ41RvJjvyI0ryXD+cs6yx+jyGo8v89Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.dll", "FileLength": 28199, "LastWriteTime": "2025-05-30T11:27:03.5649396+00:00"}, "tZlrvGHHtRxZtqnBSy2uArNM6yrTycFfcaF5ug8BG/s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\wk615gsm3x-0mbixa0emp.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Tasks.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t00a3ii73t", "Integrity": "Y5YyeYeK5bX50HlzK/5fJ2MVeM6k2vx5IQQT/jla5AY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Tasks.dll", "FileLength": 10136, "LastWriteTime": "2025-05-30T11:27:03.6539376+00:00"}, "icBel0gw+SHoEHVCkEwi8ECUbRr/FaDY4vUnS/9NP/Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\4yp9u04pi8-ywkcbcpvn6.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Thread.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jvj60dq0l4", "Integrity": "4WHka4U32OHZaDFHYXciPMh9Oz8NvxOdoS5Ei93Sqr0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Thread.dll", "FileLength": 9829, "LastWriteTime": "2025-05-30T11:27:03.7099384+00:00"}, "ePv7Oz2ME7yfAZYziCyTpovACnqwCdGFFLiNfsceZb0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\vvay7lm8dw-gfmuk6ea5j.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.ThreadPool.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bg34mp531e", "Integrity": "hSuwYZjfd+HKuyjXMTLz+7YHTSY6xFegz/uTZsGgnTE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.ThreadPool.dll", "FileLength": 9742, "LastWriteTime": "2025-05-30T11:27:03.7849384+00:00"}, "vGWa1+rROg+VSugcucr36pBxAn9QZtcgw46XCz0f/Mc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8388v1hi10-iu4dpifc8h.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Timer.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ujjgj830je", "Integrity": "+glAS2SgvXBJg3mpGbQfDr9sSYk+vlQPUrnxJzi6wR8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.Timer.dll", "FileLength": 9660, "LastWriteTime": "2025-05-30T11:27:03.8079376+00:00"}, "4tlGrxAed8BhzFTWWY/6NTRX5Mk5wMo5igJ0V4nCFBs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\nz84sx76oa-9k8sbhjygk.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6sahkghd18", "Integrity": "KovO7FysRwigCrOrM83yM0T15TvaMr69csQENTa7E54=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Threading.dll", "FileLength": 22504, "LastWriteTime": "2025-05-30T11:27:03.8619382+00:00"}, "jT1VlNlqDqKGBy2SgJIPSZTFnIuXO5ge60gX9zEAK+g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\na20idneau-1k764rigf6.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Transactions.Local.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ugki8g03hj", "Integrity": "4r+TR2cxDQbaiXWn5DdlwfKqlB0CAAb4n7nQei6Qq0w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Transactions.Local.dll", "FileLength": 60099, "LastWriteTime": "2025-05-30T11:27:03.9699365+00:00"}, "9cHxn3rmmaS4CmvH1cIBUg5csoty2BMabDDtBHazSxc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\pxah69h6s5-w3k32pmv05.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Transactions.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dnnh11vhaa", "Integrity": "+QYIcQGtmH0XCEDY3Nf656mpzeaAv3hVOyesp6TrItY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Transactions.dll", "FileLength": 10106, "LastWriteTime": "2025-05-30T11:27:04.0199366+00:00"}, "jrcF4V74x4spLFXPZz4gCUMQSR2QZW6aoZN+HoKb3zE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\pexla0egtj-40rat1p1lb.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ValueTuple.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2b4uqjjjkr", "Integrity": "shhJcH9TEAeKM59QAbaVweyla9c/+d5vY0rSMtpDe1A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.ValueTuple.dll", "FileLength": 9662, "LastWriteTime": "2025-05-30T11:27:04.0489371+00:00"}, "3QqzkAjfEWoZGua1uqoDmjUEGivQ9JTSDaMXiT2gv28=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\smrc2bufpw-edmlcfioac.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Web.HttpUtility.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rnk1ervpbw", "Integrity": "12i5LUlvUQVsMB5xNxx13MBnASdUBMtwGXU2JQ0QkX4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Web.HttpUtility.dll", "FileLength": 16297, "LastWriteTime": "2025-05-30T11:27:04.0789365+00:00"}, "9eO3jpfBMOalq+iuffSSMeKreDxs6x1AWM63rHbeL7s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\v09rql7tft-k46elbqg18.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Web.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5u9fp4xgxp", "Integrity": "c8cP9v85tGPnut/lXzs3Bk9Ka1R1fWY9+/TF3JCIqUo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Web.dll", "FileLength": 9849, "LastWriteTime": "2025-05-30T11:27:04.1209397+00:00"}, "wOl7KFVvsgnINhWPDl6mgtW0Xc3RqlkeRnv05A2xZYg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\htyef4zmk3-qiqpq3hh91.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Windows.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "348nr99ngr", "Integrity": "LWhAhJw/iSfp4oN0sUtFAYz6XKKl6Gr/Ts7A0x3DMXE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Windows.dll", "FileLength": 9994, "LastWriteTime": "2025-05-30T11:27:04.184942+00:00"}, "WxKsRKkoDb4pFNNQQnQSDGpobYV93fnbwyhjAOyeJKY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\8b1a4o4nwi-mmjs6yp1dc.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.Linq.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tscmu3o6n7", "Integrity": "TbSydaz3YmZ8cl73vnL0Gd00Gq45xza1V92e/ShkDWM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.Linq.dll", "FileLength": 9962, "LastWriteTime": "2025-05-30T11:27:04.1989393+00:00"}, "2FefMRCsfSk+2AigRGy7h2oABAAsn2KdIGXxLzttKjY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\4xsx886wzx-0pc4fu4u96.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1inerez2b5", "Integrity": "eRHOrwOHCsMDxTcWRJnTq5KYBcbNwJaP9iFSE6K6Ij0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.dll", "FileLength": 11590, "LastWriteTime": "2025-05-30T11:27:04.2169437+00:00"}, "F+hNZefmWJjQrhGwpCfuKcPqZ2XT6QnYs9a+5HxQ3GA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\g1qwt775vk-cylb5ulnt4.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.Serialization.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ieit9grs3x", "Integrity": "lmQ9y5Hw8pKm3ACG7Lf7xSBxJnjDT/GO2qtM6F5+WKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.Serialization.dll", "FileLength": 9997, "LastWriteTime": "2025-05-30T11:27:04.2269397+00:00"}, "JYkBU9qoGjPSXw6HNZoxYpGGUYlHTmOF0nidkGoGvM8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\yu822o1oeh-kbwiliq8rw.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XDocument.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ddy0edlbbp", "Integrity": "/x9GBjFNeCvJME2QRIAfQMrhl8euqB4t7RS/na3X4dE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XDocument.dll", "FileLength": 9870, "LastWriteTime": "2025-05-30T11:27:04.2359413+00:00"}, "l9kqDd2c668cWoXQ0zI0mPtS0Q5CVR1Xq8snT9fBmo0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\op5fsjsqc2-te0nflyf5a.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lu6vvwnwum", "Integrity": "baPdVf2d4BBNOkuLTivm4FIKMOclKPDMmyxr2NHtoBU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.dll", "FileLength": 10226, "LastWriteTime": "2025-05-30T11:27:04.2429383+00:00"}, "yeBKGFZg4+6t3/S2Zp4s3d4OBKTGh/WoNSGg3SFB1t0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\4w513a58fw-4575qw5gpx.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XPath.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ypl8r2wwyk", "Integrity": "9mJLSQps/PYP5C5eyS29/bLKLzKdpTUXL3TgD33kDG0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XPath.dll", "FileLength": 9790, "LastWriteTime": "2025-05-30T11:27:04.253938+00:00"}, "qqWBEEq9L1FHkj4C2HxPVEioqPAJ6Gu/GfU4uRGQNG0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\m0qli7wbnp-fgo60cdtgs.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XmlDocument.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "93ihdlb88q", "Integrity": "1cbQhm2QibA3cfIpQqqy1FFGZ4Vm0TuLW7/tow4SLWA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XmlDocument.dll", "FileLength": 9876, "LastWriteTime": "2025-05-30T11:27:04.2629429+00:00"}, "lf0AO/018kh4b75FCRI4waMOFtHZ9bLSbejjL2Xu7O8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\crut1veyzw-4ac1hjiwuv.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6hd6d9lpe2", "Integrity": "RvWnTy0NFv3cNQoAgrXyPOxqYCgNDHuKYrTDofRGu9U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.dll", "FileLength": 10380, "LastWriteTime": "2025-05-30T11:27:04.276939+00:00"}, "L/qr6Mr6aICCXLv5j9UXslr2fcvlQ7NHeFxR8OzQrkw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\iebaqf4uk0-al90pvkex2.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2can1dxhgj", "Integrity": "7iYsrUOeTTuEKanpLa6PEjMav+kUEItyAKL4Dq1vghU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Xml.dll", "FileLength": 12244, "LastWriteTime": "2025-05-30T11:27:04.2939468+00:00"}, "qMzce7CTyWJ2Ri4l9kjPJirog2cIh0DWeieNRv76oyw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\wxfyzc9dpg-rt1s4rjser.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nyduio33hf", "Integrity": "L0SHxA3nrdFSXHhOaBv15BJShWRlEEnyvxUg/tvCr/g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.dll", "FileLength": 19810, "LastWriteTime": "2025-05-30T11:27:04.3049435+00:00"}, "jBiWjbtVo2ydk1bpAYn/jL0EaiJlDyfFJpEfjJS6eR0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\87llkqkxxc-tfsmmzxmfx.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\WindowsBase.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x8hlbuhg66", "Integrity": "b4JFjS4/t12wkr9g84D9gxrfeDhJWdiHRtwbNoz8+io=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\WindowsBase.dll", "FileLength": 10224, "LastWriteTime": "2025-05-30T11:27:04.3339378+00:00"}, "xdOi/Xel8Ge/OV+APXpC2S7mwTytduTFZSiDc3+pIbI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\ujo0e02x1a-li8wpogyle.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\mscorlib.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pzd11qsmjb", "Integrity": "FYZ9tNpo7cxEVNrcE95d6OMAUBxU/2fJfMwq0pSKeLk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\mscorlib.dll", "FileLength": 23300, "LastWriteTime": "2025-05-30T11:27:04.3419383+00:00"}, "H9jBEoRpyTU23/G5EM52xib4Ob8304i7Bu30+3oxgTk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\idn0z5pxs8-31xsyeymyc.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\netstandard.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ianw03nc7f", "Integrity": "FmbQdW5q9FawwZpegK/OO5/xnCjIFiFHuB2uFpd0ylc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\netstandard.dll", "FileLength": 33648, "LastWriteTime": "2025-05-30T11:27:04.3669362+00:00"}, "rgB/TCBvpFG+wsPOrHPo0RqUj7ocro/VL6RHvZs4L+Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\gg80zaiuzy-wdurtfckma.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.CoreLib.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oveeqw49pf", "Integrity": "LGNlnoKmIM2MnxvSJz7fVoobPTrRGWGOkkrHlZMEtTo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\System.Private.CoreLib.dll", "FileLength": 1319160, "LastWriteTime": "2025-05-30T11:27:04.4730024+00:00"}, "+0f/8U1pIjmYhj6nVL5KE3gvd3OgzKyle1puRCDLnqc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\3u3u7nk1bw-70g2k3m3o7.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "88p4i4ki7b", "Integrity": "SAVUk5AfjmGKP5uSta5VNEvI2xNtDnDm6Hfx0miL11A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 69087, "LastWriteTime": "2025-05-30T11:27:04.4830032+00:00"}, "FAztQszHZzVjSZa1oLlGtmN5mq6S0WYqFW0Dph2pN6c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\hrcrkc0nws-18qbr72j4r.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.timezones.blat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\dotnet.timezones.blat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "idm057nr12", "Integrity": "RkKvKPBseWcHwX+wqjRE5fhpQCI7suH8+5iyJdkBlFA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\dotnet.timezones.blat", "FileLength": 72772, "LastWriteTime": "2025-05-30T11:27:04.4930029+00:00"}, "FgThsVcuZhZa7NRG/AiL/MmiZjPnDBZSnzb9dQNKv1w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\u1yvmuk1jg-kxj591kajg.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\dotnet.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9muf40zexe", "Integrity": "P4OetdVZbOSWuRBHq6/A6KWbSeraSCrshNKQnAqISA4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\dotnet.wasm", "FileLength": 1049902, "LastWriteTime": "2025-05-30T11:27:04.6040024+00:00"}, "wvcP8HVnUUHk+zinwHKzXfmG2jxneX0hiq3G/Zsb5f8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\x9ep50nd4y-tjcz0u77k5.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffu5aujli6", "Integrity": "Sxt3k51yp5RyINpi7a/YQNnTWexafADiNQYBnBjQrYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 359724, "LastWriteTime": "2025-05-30T11:27:04.6540053+00:00"}, "YAc8sybiaSpWFxDF250dK71RLmO7UU33IeTxkAubV10=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\qrklo7emmz-tptq2av103.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcp4n5gllp", "Integrity": "rLsEn/DgWXf7nRK2qegv2ARpYrcwixMOaXOoFcVmgjg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 220055, "LastWriteTime": "2025-05-30T11:27:04.6970009+00:00"}, "WzTGqhgF0sTvwROlNFOM/IyqF69+7pIq7SeE7i0cAL4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\kbe3rjeggr-lfu7j35m59.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m3twp0zama", "Integrity": "UsST+ZWYFgogrz0pZB/4gGQWboPgRkurfdpi/0/ENCA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 347023, "LastWriteTime": "2025-05-30T11:27:04.7390006+00:00"}, "K3OC9mTHEIh7pCcdilKmQJbsOCZzHQGcDr/bm/8Tmcw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\45tjy1fjzd-7cxe8oq9xu.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/emcc-props.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\emcc-props.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y184wmhugi", "Integrity": "xsnkfecC0d5V5MpAn0thzajXuQf9I0JKqU2Pq+QKTus=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\emcc-props.json", "FileLength": 270, "LastWriteTime": "2025-05-30T11:27:04.7570038+00:00"}, "LHdH1n4qlTz9HzAzmsZ8rbcHH6G/nhv4+8cBZFK1c6Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\wv9qnk3jyv-e58drek8sh.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/MyBlazorApp.Shared.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\MyBlazorApp.Shared.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q55oqdi87j", "Integrity": "4gHGnovJD4S6bXC0oI3lotm+g8pqeUDt0XjWLpTuWig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\MyBlazorApp.Shared.dll", "FileLength": 2141, "LastWriteTime": "2025-05-30T11:27:04.7750029+00:00"}, "0xhBk2T29PPQrbkmp6DhxvbGdlES8fvJ3HQWT+RXIh0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\3bf2dz654v-ur027rm6ob.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/MyBlazorApp.Shared.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\MyBlazorApp.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mqeq64lm5u", "Integrity": "HvPZz13JF+VmibzAQsG2y/N6gaxadLivfu6ODf/pqWQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\MyBlazorApp.Shared.pdb", "FileLength": 6256, "LastWriteTime": "2025-05-30T11:27:04.3279388+00:00"}, "Q05WbS3sUcXcF0SdiPsBQ2f3Oa+MZQ7nkCWhUmE2TSY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\o951s3yf1h-9njaf0w28h.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/MyBlazorApp.Client.dll.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\MyBlazorApp.Client.dll", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t1sinjq3jq", "Integrity": "gWHCMSEbBGJ2g3Rto630/2rd+7YkvQz2j8dD4lIFeIw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\MyBlazorApp.Client.dll", "FileLength": 8112, "LastWriteTime": "2025-05-30T11:27:04.3379406+00:00"}, "8sLqBHsvfxM5EHrVJb+Jp6odtBCZ4bPM251JSdhUAuU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\75k6nhl0mc-dlxyh9t8us.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/MyBlazorApp.Client.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\MyBlazorApp.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "09giqdawtl", "Integrity": "PI/+9cQvEeb2W5wDj/JRFDVkSs2XHZYqgRpAuWmfWiE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\MyBlazorApp.Client.pdb", "FileLength": 18333, "LastWriteTime": "2025-05-30T11:27:04.3469402+00:00"}, "CGvxP2JiRnBtt/z0rFZ3n8IFQ2CCVEIk46QAPUSjHwg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\9vwsrg1m3q-b8ehsd2d9z.gz", "SourceId": "MyBlazorApp.Client", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\obj\\Debug\\net7.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "akctuf9924", "Integrity": "2i4P4RrHcQxcuAo0Oy+snsJe3z/QUlLfBP/VnFlvFCg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\bin\\Debug\\net7.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 8851, "LastWriteTime": "2025-05-30T13:37:49.3461389+00:00"}}, "CachedCopyCandidates": {}}