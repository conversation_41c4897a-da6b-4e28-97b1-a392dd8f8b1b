{"GlobalPropertiesHash": "HutQLitHKpiq2uy2wtdspexgRnDXn+QWeoVvc+0F1HI=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["HFPxNqTkRJtEU8VwfGruFPhDDLHXN55Bd8itNe2UogY=", "BP7jwxB5/IcbOS83j+ABAwS/zrB9bdauZ07uddOCzMQ=", "cBLDhyHAYLXuUt+hKdUQ24flmuYOoOMsM2YRw6QBv9Y=", "myyMNZPYY1gLH15dVgWadnKcDD54IjUyqvi9fsw8kXg=", "k6mgEAXAI2ALhdSE6DckNTbvkdlZJl7L0FdUfmP5ouA=", "vcR7COFdWYrKu84Zs3V5q5xBzYI7FJ5o3kmc4VDgOeM=", "pI+oxk5H3P4VwuUb+r/3zAT8R/f9i+XO9lWNR4XTaa8=", "SvboyCL5H0QNJ9XpHrD7pvRFiJp9wY1kJVk/hSAsif8=", "Kc+CC1l4dJ1T6cCCGZ5Qv2oAk71w2KQe9PaJr/D7CtA=", "Z1RdxgfvoWQ9croeaRwQd6AAVKTOBIIZfZYFCBlQeq4=", "fU3oZD0uY9jsbbLR3UwI2c9A53n1IN3AfGg272OKA00=", "01egJdhpr51joJ47PjmN3E5FWam8cWbdqGWMJGf58Ps=", "3qs8/i4sE2nBAcv5205mXhnSQtTd/sh27EKH/N0qWa0=", "n9cUWtLFKIYOcsg5Ne9BO7FJZvO3Z+tnrYXQCK52eRc=", "K+K7Fa25lcXLrm1Lp+KZRWducdULnDzjt1t4OrOYfJI=", "H0Rt/W+VcZGNggpZAEuNnkG+Dl7wHgowybC0bmVAFNc=", "ACO+QREtgokbo+V8cs7VVVnFStaCgFUsF2WvDW7vgjE=", "c3IVZ6hCw/BaesnigDBf9xPQa2A0v7Ya29UgHJ0Fg9s=", "Z9+PK2U03Waj2WXmmh/xVmRHHpxRWZ5X6wdVxkQOGWQ=", "td8k7v+N+NZ3yN6LYyLRcE9OfXLj/E7aqsDaV8CRjEQ=", "dVtWbmv0cLt1eP306hHy6wkTSmsp1ymjNaqhWwDmvtU=", "yjUizQ2VZ+IO1b0v5N+9vzq1L51lvMIYaTsuBtODNjc=", "RL0YnDpTwTqVavRsx61LM/YDfDx0QA+MhyN52u1kgY4="], "CachedAssets": {"HFPxNqTkRJtEU8VwfGruFPhDDLHXN55Bd8itNe2UogY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\app.css", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qbqo6dzkt5", "Integrity": "uhotZszkBLq/V8xt8UtpU6lGHEIqbqLsFUVGyelV2TU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 3818, "LastWriteTime": "2025-05-30T11:25:52.5809833+00:00"}, "BP7jwxB5/IcbOS83j+ABAwS/zrB9bdauZ07uddOCzMQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-05-30T11:25:51.9179843+00:00"}, "cBLDhyHAYLXuUt+hKdUQ24flmuYOoOMsM2YRw6QBv9Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-05-30T11:25:51.9199836+00:00"}, "myyMNZPYY1gLH15dVgWadnKcDD54IjUyqvi9fsw8kXg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/FONT-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "48tmkg660f", "Integrity": "jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\FONT-LICENSE", "FileLength": 4103, "LastWriteTime": "2025-05-30T11:25:51.922988+00:00"}, "k6mgEAXAI2ALhdSE6DckNTbvkdlZJl7L0FdUfmP5ouA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/font/css/open-iconic-bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cmapd0fi15", "Integrity": "BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "FileLength": 9395, "LastWriteTime": "2025-05-30T11:25:52.5829827+00:00"}, "vcR7COFdWYrKu84Zs3V5q5xBzYI7FJ5o3kmc4VDgOeM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0uw8dim9nl", "Integrity": "OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "FileLength": 28196, "LastWriteTime": "2025-05-30T11:25:52.5839823+00:00"}, "pI+oxk5H3P4VwuUb+r/3zAT8R/f9i+XO9lWNR4XTaa8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.otf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wk8x8xm0ah", "Integrity": "sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "FileLength": 20996, "LastWriteTime": "2025-05-30T11:25:52.5859835+00:00"}, "SvboyCL5H0QNJ9XpHrD7pvRFiJp9wY1kJVk/hSAsif8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sjnzgf7e1h", "Integrity": "+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "FileLength": 55332, "LastWriteTime": "2025-05-30T11:25:52.5879843+00:00"}, "Kc+CC1l4dJ1T6cCCGZ5Qv2oAk71w2KQe9PaJr/D7CtA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ll5grcv8wv", "Integrity": "p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "FileLength": 28028, "LastWriteTime": "2025-05-30T11:25:52.5889844+00:00"}, "Z1RdxgfvoWQ9croeaRwQd6AAVKTOBIIZfZYFCBlQeq4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h4d0pazwgy", "Integrity": "cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "FileLength": 14984, "LastWriteTime": "2025-05-30T11:25:52.5899828+00:00"}, "fU3oZD0uY9jsbbLR3UwI2c9A53n1IN3AfGg272OKA00=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/ICON-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4dwjve0o0b", "Integrity": "aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\ICON-LICENSE", "FileLength": 1093, "LastWriteTime": "2025-05-30T11:25:51.9239837+00:00"}, "01egJdhpr51joJ47PjmN3E5FWam8cWbdqGWMJGf58Ps=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\css\\open-iconic\\README.md", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/open-iconic/README#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5bzwdl5l6x", "Integrity": "waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\README.md", "FileLength": 3655, "LastWriteTime": "2025-05-30T11:25:51.9249911+00:00"}, "3qs8/i4sE2nBAcv5205mXhnSQtTd/sh27EKH/N0qWa0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\favicon.png", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-05-30T11:25:52.0359845+00:00"}, "n9cUWtLFKIYOcsg5Ne9BO7FJZvO3Z+tnrYXQCK52eRc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\icon-192.png", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-05-30T11:25:52.0389816+00:00"}, "K+K7Fa25lcXLrm1Lp+KZRWducdULnDzjt1t4OrOYfJI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\index.html", "SourceId": "MyBlazorApp.Client", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Accounts1\\MyBlazorApp\\Client\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ynzbvqfzlx", "Integrity": "TmCl3jZtn60fN3yyvU5bErOs3lrIsRKvnnqlVRDHHVw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1005, "LastWriteTime": "2025-05-30T11:25:52.0619988+00:00"}}, "CachedCopyCandidates": {}}