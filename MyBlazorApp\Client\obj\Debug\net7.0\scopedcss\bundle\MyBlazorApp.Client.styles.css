/* /Shared/MainLayout.razor.rz.scp.css */
.page[b-e5wuhsindb] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-e5wuhsindb] {
    flex: 1;
}

.sidebar[b-e5wuhsindb] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-e5wuhsindb] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-e5wuhsindb]  a, .top-row[b-e5wuhsindb]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-e5wuhsindb]  a:hover, .top-row[b-e5wuhsindb]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-e5wuhsindb]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row:not(.auth)[b-e5wuhsindb] {
        display: none;
    }

    .top-row.auth[b-e5wuhsindb] {
        justify-content: space-between;
    }

    .top-row[b-e5wuhsindb]  a, .top-row[b-e5wuhsindb]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-e5wuhsindb] {
        flex-direction: row;
    }

    .sidebar[b-e5wuhsindb] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-e5wuhsindb] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-e5wuhsindb]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-e5wuhsindb], article[b-e5wuhsindb] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
/* /Shared/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-9tuquqkgu0] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-9tuquqkgu0] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-9tuquqkgu0] {
    font-size: 1.1rem;
}

.oi[b-9tuquqkgu0] {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item[b-9tuquqkgu0] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-9tuquqkgu0] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-9tuquqkgu0] {
        padding-bottom: 1rem;
    }

    .nav-item[b-9tuquqkgu0]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-9tuquqkgu0]  a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item[b-9tuquqkgu0]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-9tuquqkgu0] {
        display: none;
    }

    .collapse[b-9tuquqkgu0] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
    
    .nav-scrollable[b-9tuquqkgu0] {
        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}
