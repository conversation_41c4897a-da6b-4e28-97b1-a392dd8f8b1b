{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"MyBlazorApp.Server/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.WebAssembly.Server": "7.0.18", "MyBlazorApp.Client": "1.0.0", "MyBlazorApp.Shared": "1.0.0"}, "runtime": {"MyBlazorApp.Server.dll": {}}}, "Microsoft.AspNetCore.Authorization/7.0.18": {"dependencies": {"Microsoft.AspNetCore.Metadata": "7.0.18", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1824.16924"}}}, "Microsoft.AspNetCore.Components/7.0.18": {"dependencies": {"Microsoft.AspNetCore.Authorization": "7.0.18", "Microsoft.AspNetCore.Components.Analyzers": "7.0.18"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1824.16924"}}}, "Microsoft.AspNetCore.Components.Analyzers/7.0.18": {}, "Microsoft.AspNetCore.Components.Forms/7.0.18": {"dependencies": {"Microsoft.AspNetCore.Components": "7.0.18"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1824.16924"}}}, "Microsoft.AspNetCore.Components.Web/7.0.18": {"dependencies": {"Microsoft.AspNetCore.Components": "7.0.18", "Microsoft.AspNetCore.Components.Forms": "7.0.18", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.JSInterop": "7.0.18", "System.IO.Pipelines": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1824.16924"}}}, "Microsoft.AspNetCore.Components.WebAssembly/7.0.18": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "7.0.18", "Microsoft.Extensions.Configuration.Binder": "7.0.4", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.JSInterop.WebAssembly": "7.0.18"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1824.16924"}}}, "Microsoft.AspNetCore.Components.WebAssembly.Server/7.0.18": {"runtime": {"lib/net7.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1824.16924"}}}, "Microsoft.AspNetCore.Metadata/7.0.18": {"runtime": {"lib/net7.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1824.16924"}}}, "Microsoft.Extensions.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Binder/7.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.423.11508"}}}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "System.Text.Json": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileSystemGlobbing": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "Microsoft.Extensions.Options/7.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Primitives/7.0.0": {}, "Microsoft.JSInterop/7.0.18": {"runtime": {"lib/net7.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1824.16924"}}}, "Microsoft.JSInterop.WebAssembly/7.0.18": {"dependencies": {"Microsoft.JSInterop": "7.0.18"}, "runtime": {"lib/net7.0/Microsoft.JSInterop.WebAssembly.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1824.16924"}}}, "System.IO.Pipelines/7.0.0": {}, "System.Text.Encodings.Web/7.0.0": {}, "System.Text.Json/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}}, "MyBlazorApp.Client/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": "7.0.18", "MyBlazorApp.Shared": "1.0.0"}, "runtime": {"MyBlazorApp.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MyBlazorApp.Shared/1.0.0": {"runtime": {"MyBlazorApp.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"MyBlazorApp.Server/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-nqZFpH/KxvwISfsCWeuGqwUAg4Ddo8fDo7ECv7ogjr+KZ9p2ZrVeZoV6j8908Aftz9HJx7HsHZdjoDEgbQM8xA==", "path": "microsoft.aspnetcore.authorization/7.0.18", "hashPath": "microsoft.aspnetcore.authorization.7.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Components/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-TDOH5NwP8QmkHOYXGTEne/r1m6HzQxtWI+LfoPqfQHF9dnx9hkIpIWorl/EsfWoFKAdPP+luZTf9T/lVmk3zvA==", "path": "microsoft.aspnetcore.components/7.0.18", "hashPath": "microsoft.aspnetcore.components.7.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-axsZFS2phKs2IYpcNes4L9YYFoDb2QsLKblIP2rHSmgUzxDwqYOgZcoq7OI8JS4010eXU55HS2WSxZwvzWPCGA==", "path": "microsoft.aspnetcore.components.analyzers/7.0.18", "hashPath": "microsoft.aspnetcore.components.analyzers.7.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-ig9RRk0PD9qk+pYOPMilCh4avzumNV2sHTAFfgV34lGmkgy00dCM35AlyYilKwyFJuKlBqIp5T5y+7asDxwgBQ==", "path": "microsoft.aspnetcore.components.forms/7.0.18", "hashPath": "microsoft.aspnetcore.components.forms.7.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-tht34NqViDcUKkkf15cGaai38ZTu7cPUpJ7A0bx1l7zvtne5/Y/7AwyjTLUVszeH4FpT1lQ0ZEvfIP/pKSGUUg==", "path": "microsoft.aspnetcore.components.web/7.0.18", "hashPath": "microsoft.aspnetcore.components.web.7.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-nGaOTekJ2p5VkmSLZmDKX5zVoJXD5qknCvH0GECztKuA7wDiQ/gvMwuTOvzXrNj67LzrQ86srPLsWLohl3EeHA==", "path": "microsoft.aspnetcore.components.webassembly/7.0.18", "hashPath": "microsoft.aspnetcore.components.webassembly.7.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.Server/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-6tNFwJdPT/+/P8G4Ix4YlqS7UbVxBGGnUFywR14RSGDGgr0QNW8+Ysuzm7u0qEA7txvFVsqKnLUIkpl2EClFoA==", "path": "microsoft.aspnetcore.components.webassembly.server/7.0.18", "hashPath": "microsoft.aspnetcore.components.webassembly.server.7.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-0kPo1MJAVEtHTgL4hxY7OcSREik2aDb799klMiSZfcwlKZkWlP58FbS4FbHbRP1S+PYZU2idBq1dbBj6MEWnyQ==", "path": "microsoft.aspnetcore.metadata/7.0.18", "hashPath": "microsoft.aspnetcore.metadata.7.0.18.nupkg.sha512"}, "Microsoft.Extensions.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tldQUBWt/xeH2K7/hMPPo5g8zuLc3Ro9I5d4o/XrxvxOCA2EZBtW7bCHHTc49fcBtvB8tLAb/Qsmfrq+2SJ4vA==", "path": "microsoft.extensions.configuration/7.0.0", "hashPath": "microsoft.extensions.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-8+XPvJnHZsYgHOQlcMuQe7QNF5KdVKHH1F/wW3nd8/u81Gk/XFAYMDP0Lpz18h7/AM95M662vvqMorcYxCBB4w==", "path": "microsoft.extensions.configuration.binder/7.0.4", "hashPath": "microsoft.extensions.configuration.binder.7.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xk2lRJ1RDuqe57BmgvRPyCt6zyePKUmvT6iuXqiHR+/OIIgWVR8Ff5k2p6DwmqY8a17hx/OnrekEhziEIeQP6Q==", "path": "microsoft.extensions.configuration.fileextensions/7.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LDNYe3uw76W35Jci+be4LDf2lkQZe0A7EEYQVChFbc509CpZ4Iupod8li4PUXPBhEUOFI/rlQNf5xkzJRQGvtA==", "path": "microsoft.extensions.configuration.json/7.0.0", "hashPath": "microsoft.extensions.configuration.json.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==", "path": "microsoft.extensions.fileproviders.abstractions/7.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K8D2MTR+EtzkbZ8z80LrG7Ur64R7ZZdRLt1J5cgpc/pUWl0C6IkAUapPuK28oionHueCPELUqq0oYEvZfalNdg==", "path": "microsoft.extensions.fileproviders.physical/7.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jONjKHiF+E92ynz2ZFcr9OvxIw+rTGMPEH+UZGeHTEComVav93jQUWGkso8yWwVBcEJGcNcZAaqY01FFJcj7w==", "path": "microsoft.extensions.filesystemglobbing/7.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pkeBFx0vqMW/A3aUVHh7MPu3WkBhaVlezhSZeb1c9XD0vUReYH1TLFSy5MxJgZfmz5LZzYoErMorlYZiwpOoNA==", "path": "microsoft.extensions.logging.abstractions/7.0.1", "hashPath": "microsoft.extensions.logging.abstractions.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pZRDYdN1FpepOIfHU62QoBQ6zdAoTvnjxFfqAzEd9Jhb2dfhA5i6jeTdgGgcgTWFRC7oT0+3XrbQu4LjvgX1Nw==", "path": "microsoft.extensions.options/7.0.1", "hashPath": "microsoft.extensions.options.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.JSInterop/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-NP/QLcewoUpsDrX8J4hLd5Yz35F7H44K0Zr+7QEd/Jna7oovecaNN+4Uj05qHuEDx4vMQnqhsZN6MFZNxrdTlA==", "path": "microsoft.jsinterop/7.0.18", "hashPath": "microsoft.jsinterop.7.0.18.nupkg.sha512"}, "Microsoft.JSInterop.WebAssembly/7.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-h8g3M/7ln46g1hEefWhulfLDqCKERPevFmEuVzHke8eJ//Amxi/dOy3gSlPe7PAr65YZNl7FaFCeliRbYMmHYA==", "path": "microsoft.jsinterop.webassembly/7.0.18", "hashPath": "microsoft.jsinterop.webassembly.7.0.18.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}, "MyBlazorApp.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MyBlazorApp.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}